<template>
  <view class="task-card">
    <!-- 任务标题、日期和任务类型 -->
    <view class="task-header">
      <view class="task-header-avatar">
        <image class="task-header-avatar-img" src="/static/xun_icon.png" v-if="taskInfo.taskType === 10"/>
        <image class="task-header-avatar-img" src="/static/gai_icon.png" v-else/>
      </view>
      <view class="task-header-right">
        <view class="task-title">
          <span> {{ taskInfo.taskType === 10 ? "计划任务" : "整改任务" }} </span>
          <text class="task-date">{{ formatTimeAgo(taskInfo.createdAt) }}</text>
        </view>
        <!-- 任务描述 -->
        <view class="task-desc">{{ taskInfo.taskTitle }}</view>
      </view>
    </view>
    <!-- 任务类型 -->
    <view class="task-type">{{ taskInfo.taskType === 10 ? "计划任务" : "整改任务" }}</view>

    <!-- 增加一个进度条 -->
    <view class="progress-container"
          v-if="taskInfo.taskType === 10 &&  taskInfo.qcPointsTotal !== undefined && taskInfo.qcPointsTotal !== 0">
      <view class="progress-label">
        <text v-if=" taskInfo.qcPointsCompleted >= miniCompletionCount ">完成进度（已达标）</text>
        <text v-else>完成进度（{{ taskInfo.qcPointsCompleted }} / {{ miniCompletionCount }}）</text>
        <text class="progress-percent" :class="{'rate-warning': !isReachedMinRate()}">{{ getCompletionRate() }}%</text>
      </view>
      <view class="progress-bar">
        <view class="progress-track"></view>
        <view class="progress-fill"
              :class="{'progress-fill-warning': !isReachedMinRate()}"
              :style="{ width: getCompletionRate() + '%' }"></view>
        <view class="min-completion-mark" v-if="taskInfo.minCompletionRate"
              :style="{ left: taskInfo.minCompletionRate + '%' }">
          <view class="min-mark-line"></view>
          <!-- <text class="min-mark-label">{{ taskInfo.minCompletionRate }}%</text> -->
        </view>
      </view>
    </view>

    <!-- 巡查地点列表 -->
    <location-list :locations="taskInfo.location" v-if="taskInfo.taskType === 10"></location-list>
    <!-- 执行时间范围 -->
    <view class="time-range">要求执行时间：
      {{ formatDate(taskInfo.beginDate) }}
      至
      {{ formatDate(taskInfo.endDate) }}
    </view>
    <view class="line"></view>
    <!-- 操作按钮 -->
    <view class="task-footer">
      <!-- 任务状态 -->
      <text class="task-status">
        {{ TaskStatus.getDescription(taskInfo.status) }}
      </text>
      <button class="accept-btn" @click="onAccept" v-if="taskInfo.status === TaskStatus.PENDING_ACCEPT">接受</button>
      <button class="accept-btn " @click="goToExecute" v-else-if="taskInfo.status === TaskStatus.COMPLETED">去查看
      </button>
      <button class="accept-btn accept-btn-green" @click="goToExecute" v-else>去执行</button>
    </view>
  </view>
</template>

<script setup>
import {computed} from "vue";
// 引入子组件
import LocationList from '../locationList/LocationList.vue';
import {formatDate, formatTimeAgo} from "@/utils/date"
import * as TaskApi from '@/api/tasks'
import {TaskStatus} from "@/common/enum/taskStatus";

// Props 接收父组件传入的数据
const props = defineProps({
  taskInfo: {
    type: Object,
    required: true,
  }
})

// Emits 事件
const emit = defineEmits(['accept']);

// 巡查率
const miniCompletionCount = computed(() => {
  const count = props.taskInfo.qcPointsTotal * props.taskInfo.minCompletionRate * 0.01
  return Math.ceil(count)
})


// 计算完成率
const getCompletionRate = () => {
  if (!props.taskInfo.qcPointsTotal || props.taskInfo.qcPointsTotal === 0) {
    return 0;
  }
  const rate = (props.taskInfo.qcPointsCompleted / props.taskInfo.qcPointsTotal) * 100;
  return Math.round(rate);
}

// 判断是否达到最低完成率
const isReachedMinRate = () => {
  if (!props.taskInfo.minCompletionRate) {
    return true; // 如果没有设置最低完成率，则认为已达标
  }
  return getCompletionRate() >= props.taskInfo.minCompletionRate;
}

// 接受任务
const onAccept = () => {
  TaskApi.receive(props.taskInfo.id).then(res => {
    props.taskInfo.status = TaskStatus.PENDING_EXECUTE

    uni.showToast({
      title: res.message,
      mask: true,
      duration: 1500,
    })

    setTimeout(() => {
      // 向父组件传递事件
      emit('accept', props.taskInfo.id)// 向父组件传递事件
      emit('accept', props.taskInfo.id)
    }, 1000)
  })
}

// 去执行
const goToExecute = () => {
  let url = '/pages/tasks/detail/detail-v2?tasksId='
  if (props.taskInfo.taskType === 20) {
    url = '/pages/tasks/detail/publish?tasksId='
  }
  uni.navigateTo({
    url: url + props.taskInfo.id
  })
}

</script>

<style>
.task-card {
  position: relative;
  background-color: #fff;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  margin-bottom: 16px;
}

.task-header {
  display: flex;
  flex-direction: row;
  margin-bottom: 8px;
  align-items: flex-start;
}

.task-header .task-header-avatar {
  width: 100rpx;
  height: 100rpx;
  overflow: hidden;
  flex-shrink: 0;
  border-radius: 100%;
}

.task-header-avatar .task-header-avatar-img {
  width: 100rpx;
  height: 100rpx;
  display: block;
}

.task-header-right {
  display: flex;
  padding-left: 20rpx;
  flex-direction: column;
  flex-grow: 1;
}

.task-title {
  height: 40rpx;
  line-height: 40rpx;
  font-size: 16px;
  font-weight: bold;
}

.task-date {
  font-size: 12px;
  color: #999;
  padding-left: 10rpx;
}

.task-desc {
  font-size: 14px;
  color: #333;
}

.task-type {
  position: absolute;
  font-size: 12px;
  color: #9cb9e0;
  font-weight: bold;
  right: 0;
  top: 0;
  padding: 8px 12px;
  background-color: #e5f2fa;
  border-radius: 0 0 0 10px;
}

.time-range {
  font-size: 26rpx;
  color: #b4b4b4;
  margin-bottom: 30rpx;
}

.task-footer {
  display: flex;
  align-items: center;
  margin-top: 30rpx;
}

.task-status {
  font-size: 16px;
  color: #e7a971;
}

.accept-btn {
  padding: 0px 20px;
  background-color: #007aff;
  color: #fff;
  text-align: center;
  border-radius: 10px;
  font-size: 14px;
  border: none;
  margin-left: auto;
  margin-right: 0;
}

.accept-btn-green {
  background-color: #5ac725;
}

/* 进度条样式 */
.progress-container {
  padding: 15rpx 10rpx;
  margin: 10rpx 0 20rpx 0;
}

.progress-label {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10rpx;
  font-size: 14px;
  color: #666;
}

.progress-percent {
  font-weight: bold;
  color: #333;
}

.rate-warning {
  color: #ff6a6a;
}

.progress-bar {
  position: relative;
  height: 16rpx;
  border-radius: 8rpx;
  overflow: visible;
}

.progress-track {
  position: absolute;
  width: 100%;
  height: 100%;
  background-color: #f0f0f0;
  border-radius: 8rpx;
}

.progress-fill {
  position: absolute;
  height: 100%;
  background: linear-gradient(90deg, #ff6a6a, #5ac725);
  border-radius: 8rpx;
  transition: width 0.3s ease;
}

.progress-fill-warning {
  background: linear-gradient(90deg, #ff6a6a, #ff9f60);
}

.min-completion-mark {
  position: absolute;
  width: 2rpx;
  height: 16rpx;
  z-index: 2;
}

.min-mark-line {
  position: absolute;
  width: 4rpx;
  height: 16rpx;
  background-color: #ff6a6a;
}

.min-mark-label {
  position: absolute;
  top: -30rpx;
  left: -15rpx;
  font-size: 12px;
  color: #ff6a6a;
  white-space: nowrap;
}

.line {
  width: 100%;
  height: 1px;
  background-color: #ddd;
}
</style>
