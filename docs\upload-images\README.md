# ImageUploader 图片上传组件

基于 uniapp 原生组件开发的图片上传组件，支持图片选择、上传、预览、删除等功能。

## 功能特性

- ✅ 图片选择（相册/相机）
- ✅ 图片上传（支持多张）
- ✅ 图片预览（点击放大查看）
- ✅ 图片删除（确认删除）
- ✅ 数量限制
- ✅ 上传状态显示（上传中、成功、失败）
- ✅ 禁用状态
- ✅ 必填标识
- ✅ 响应式布局

## 基本用法

### 1. 引入组件

```vue
<template>
  <view>
    <ImageUploader 
      v-model="imageList"
      title="上传图片"
      :max-count="6"
      @change="handleChange"
    />
  </view>
</template>

<script setup>
import { ref } from 'vue'
import ImageUploader from '@/components/ImageUploader/ImageUploader.vue'

const imageList = ref([])

const handleChange = (list) => {
  console.log('图片列表变化:', list)
}
</script>
```

### 2. 预设图片数据

```vue
<template>
  <ImageUploader 
    v-model="imageList"
    title="产品图片"
    :max-count="9"
  />
</template>

<script setup>
import { ref } from 'vue'

const imageList = ref([
  {
    url: 'https://example.com/image1.jpg',
    status: 'success'
  },
  {
    url: 'https://example.com/image2.jpg', 
    status: 'success'
  }
])
</script>
```

## API 文档

### Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| v-model | Array | [] | 图片列表，支持双向绑定 |
| title | String | '' | 组件标题 |
| max-count | Number | 9 | 最大上传数量 |
| disabled | Boolean | false | 是否禁用 |
| required | Boolean | false | 是否显示必填标识 |
| show-tip | Boolean | true | 是否显示提示信息 |
| quality | Number | 80 | 图片质量(0-100) |
| source-type | Array | ['album', 'camera'] | 图片来源类型 |
| size-type | Array | ['compressed', 'original'] | 图片尺寸类型 |

### Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| change | (imageList) | 图片列表变化时触发 |
| upload-success | ({ index, url, item }) | 单张图片上传成功时触发 |
| upload-error | ({ index, error, item }) | 单张图片上传失败时触发 |

### 数据格式

图片对象格式：

```javascript
{
  path: 'temp://xxx.jpg',    // 本地临时路径
  url: 'https://xxx.jpg',    // 服务器URL
  status: 'success'          // 状态: uploading | success | error
}
```

## 高级用法

### 1. 必填验证

```vue
<template>
  <ImageUploader 
    v-model="imageList"
    title="身份证照片"
    :max-count="2"
    :required="true"
  />
</template>
```

### 2. 禁用状态

```vue
<template>
  <ImageUploader 
    v-model="imageList"
    title="查看图片"
    :disabled="true"
  />
</template>
```

### 3. 自定义图片质量和来源

```vue
<template>
  <ImageUploader 
    v-model="imageList"
    title="高清图片"
    :quality="100"
    :source-type="['camera']"
    :size-type="['original']"
  />
</template>
```

### 4. 监听上传事件

```vue
<template>
  <ImageUploader 
    v-model="imageList"
    @upload-success="onUploadSuccess"
    @upload-error="onUploadError"
  />
</template>

<script setup>
const onUploadSuccess = ({ index, url, item }) => {
  console.log('上传成功:', url)
  uni.showToast({
    title: '上传成功',
    icon: 'success'
  })
}

const onUploadError = ({ index, error, item }) => {
  console.error('上传失败:', error)
  uni.showToast({
    title: '上传失败',
    icon: 'none'
  })
}
</script>
```

## 样式定制

组件使用 scoped 样式，如需自定义样式，可以通过以下方式：

### 1. 全局样式覆盖

```css
/* 在全局样式文件中 */
.image-uploader .add-btn {
  border-color: #007aff !important;
}

.image-uploader .add-icon {
  color: #007aff !important;
}
```

### 2. 深度选择器

```vue
<style>
.custom-uploader :deep(.add-btn) {
  border-color: #007aff;
}

.custom-uploader :deep(.add-icon) {
  color: #007aff;
}
</style>
```

## 注意事项

1. **依赖上传API**: 组件依赖 `@/api/upload` 中的 `uploadFile` 方法
2. **图片格式**: 支持常见图片格式 (jpg, png, gif 等)
3. **文件大小**: 建议控制单张图片大小在 5MB 以内
4. **网络环境**: 上传功能需要网络连接
5. **权限申请**: 使用相机功能需要申请相机权限

## 兼容性

- ✅ H5
- ✅ 小程序 (微信、支付宝、百度等)
- ✅ App (Android/iOS)

## 更新日志

### v1.0.0
- 初始版本发布
- 支持基本的图片上传功能
- 支持图片预览和删除
- 支持数量限制和状态显示
