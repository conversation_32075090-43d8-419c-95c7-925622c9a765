<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>主管巡查系统 - 软件著作权申请代码</title>
    <style>
        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            line-height: 1.6;
            margin: 40px;
            color: #333;
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #34495e;
            margin-top: 30px;
            border-left: 4px solid #3498db;
            padding-left: 15px;
        }
        h3 {
            color: #7f8c8d;
            margin-top: 20px;
        }
        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            font-family: "Consolas", "Monaco", monospace;
            font-size: 12px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        .description {
            background-color: #e8f4fd;
            border-left: 4px solid #3498db;
            padding: 10px 15px;
            margin: 10px 0;
            font-style: italic;
        }
        .file-path {
            color: #e74c3c;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .page-break {
            page-break-before: always;
        }
    </style>
</head>
<body>
    <h1>主管巡查系统</h1>
    <h1>软件著作权申请代码文档</h1>
    
    <div class="description">
        <strong>项目简介：</strong>主管巡查系统是一款基于uni-app开发的移动应用，旨在帮助主管人员高效管理和执行巡查工作。该系统支持多平台部署（H5、微信小程序等），为现场巡查工作提供全流程数字化解决方案。
    </div>

    <div class="description">
        <strong>技术栈：</strong>Vue 3 + uni-app + Pinia + uview-plus + Vite + dayjs + vue-i18n
    </div>

    <h2>1. 项目配置文件</h2>
    
    <h3>1.1 应用清单配置</h3>
    <div class="file-path">文件路径: src/manifest.json</div>
    <div class="description">应用的基本配置信息，包括应用名称、版本、权限等</div>
    <div class="code-block">{
    "name" : "主管巡查",
    "appid" : "__UNI__120B055",
    "description" : "",
    "versionName" : "1.2.1",
    "versionCode" : 129,
    "transformPx" : false,
    "app-plus" : {
        "compatible" : {
            "ignoreVersion" : true
        },
        "usingComponents" : true,
        "nvueStyleCompiler" : "uni-app",
        "compilerVersion" : 3,
        "modules" : {
            "Camera" : {},
            "Push" : {}
        }
    }
}</div>

    <h3>1.2 页面路由配置</h3>
    <div class="file-path">文件路径: src/pages.json</div>
    <div class="description">定义应用的页面路由和导航栏配置</div>
    <div class="code-block">{
  "pages": [
    {
      "path": "pages/index/index",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/login/index",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/tasks/list/list",
      "style": {
        "navigationBarTitleText": "任务列表",
        "navigationStyle": "default",
        "enablePullDownRefresh": true
      }
    },
    {
      "path": "pages/tasks/publish/index",
      "style": {
        "navigationBarTitleText": "发布",
        "navigationStyle": "default"
      }
    }
  ],
  "globalStyle": {
    "pageOrientation": "portrait",
    "navigationBarTextStyle": "black",
    "navigationBarTitleText": "主管巡查系统",
    "navigationBarBackgroundColor": "#F8F8F8",
    "backgroundColor": "#F8F8F8"
  }
}</div>

    <h3>1.3 构建配置</h3>
    <div class="file-path">文件路径: vite.config.js</div>
    <div class="description">Vite构建工具配置，包括代理设置和别名配置</div>
    <div class="code-block">import { defineConfig, loadEnv } from 'vite'
import uni from '@dcloudio/vite-plugin-uni'
import { resolve } from 'path'

export default defineConfig(({ mode }) => {
    const env = loadEnv(mode, process.cwd())
    
    return {
        plugins: [uni()],
        resolve: {
            alias: {
                '@': resolve(__dirname, 'src'),
            }
        },
        server: {
            port: 5183,
            proxy: {
                '^/app-api': {
                    target: env.VITE_API_BASE_URL,
                    changeOrigin: true,
                    rewrite: (path) => path.replace(/^\/app-api/, '/app-api'),
                }
            }
        }
    }
})</div>

    <div class="page-break"></div>
    <h2>2. 应用入口文件</h2>
    
    <h3>2.1 主入口文件</h3>
    <div class="file-path">文件路径: src/main.js</div>
    <div class="description">应用的主入口文件，负责创建Vue应用实例和全局配置</div>
    <div class="code-block">import {
    createSSRApp
} from "vue"
import App from "./App.vue"
import * as Pinia from "pinia"
import pushService from '@/utils/push'

console.log(">>>>>>应用运行的模式: ",  process.env.NODE_ENV)
console.log('>>>>>>请求地址:', import.meta.env.VITE_API_BASE_URL)

export function createApp() {
    const app = createSSRApp(App)
    app.use(Pinia.createPinia())
    
    // 全局注册推送服务
    app.config.globalProperties.$push = pushService
    
    return {
        app,
        Pinia,
    }
}</div>

    <h3>2.2 应用根组件</h3>
    <div class="file-path">文件路径: src/App.vue</div>
    <div class="description">应用的根组件，处理应用生命周期和全局初始化</div>
    <div class="code-block">&lt;script&gt;
export default {
  async onLaunch() {
    console.log('🚀 App Launch')

    // 初始化推送服务
    // #ifdef APP-PLUS
    try {
      await this.$push.init()
    } catch (error) {
      console.error('推送初始化失败:', error)
    }
    // #endif
  },

  onShow() {
    console.log('👀 App Show')

    // 清除应用角标
    // #ifdef APP-PLUS
    plus.runtime.setBadgeNumber(0)
    // #endif
  },

  onHide() {
    console.log('🙈 App Hide')
  },
}
&lt;/script&gt;</div>

    <div class="page-break"></div>
    <h2>3. 状态管理</h2>
    
    <h3>3.1 用户状态管理</h3>
    <div class="file-path">文件路径: src/stores/user.js</div>
    <div class="description">使用Pinia管理用户登录状态、用户信息等全局状态</div>
    <div class="code-block">import {defineStore} from 'pinia'
import * as LoginApi from '@/api/login'
import * as UserApi from '@/api/user'
import storage from '@/utils/storage'
import {ref, computed} from 'vue'

const ACCESS_TOKEN = 'AccessToken'
const USER_ID = 'userId'

// 用户状态管理
export const useUserStore = defineStore('user', () => {
    const token = ref(storage.get(ACCESS_TOKEN) || '')
    const userId = ref(storage.get(USER_ID) || 0)
    const hospitalId = ref('')
    const userInfo = ref(null)

    // 登录状态判断
    const isLogin = computed(() => {
        return !!token.value && userId.value !== 0
    })

    // 登录方法
    const login = async (username, password) => {
        try {
            const res = await LoginApi.login(username, password)
            if (res.code === 200) {
                const expiryTime = 30 * 86400
                token.value = res.data.token
                userId.value = res.data.uid
                hospitalId.value = res.data.hospitalId || ''
                storage.set(USER_ID, res.data.uid, expiryTime)
                storage.set(ACCESS_TOKEN, res.data.token, expiryTime)
                return res.data
            }
        } catch (error) {
            throw error
        }
    }

    // 获取用户信息
    const fetchUserInfo = async (force = false) => {
        try {
            if (!force && userInfo.value) {
                return userInfo.value
            }
            const result = await UserApi.getUserInfo()
            userInfo.value = result.data
            hospitalId.value = result.data.hospitalId || ''
            return userInfo.value
        } catch (error) {
            console.error("获取用户信息失败", error)
            throw error
        }
    }

    // 登出方法
    const logout = () => {
        if (userId.value > 0) {
            storage.remove(USER_ID)
            storage.remove(ACCESS_TOKEN)
            token.value = ''
            userId.value = 0
            userInfo.value = null
        }
    }

    return {
        token,
        userId,
        hospitalId,
        userInfo,
        isLogin,
        login,
        fetchUserInfo,
        logout
    }
})</div>

    <div class="page-break"></div>
    <h2>4. 网络请求模块</h2>
    
    <h3>4.1 请求封装</h3>
    <div class="file-path">文件路径: src/utils/request/index.js</div>
    <div class="description">封装网络请求，统一处理请求头、错误处理、加载状态等</div>
    <div class="code-block">import request from './request'
import {useUserStore} from '@/stores/user'

let userStore = null
let apiUrl = process.env.VUE_APP_PLATFORM !== 'h5' ? import.meta.env.VITE_API_BASE_URL :""

// 创建请求实例
const $http = new request({
    // 接口请求地址
    baseUrl: apiUrl,
    // 服务器本地上传文件地址
    fileUrl: apiUrl,
    // 服务器上传图片默认url
    defaultUploadUrl: 'upload/image',
    // 设置请求头
    header: {
        'content-type': 'application/json;charset=utf-8'
    },
    // 请求超时时间
    timeout: 15000,
    // 默认配置
    config: {
        // 是否自动提示错误
        isPrompt: true,
        // 是否显示加载动画
        load: true,
        // 是否使用数据工厂
        isFactory: true
    }
})

// 请求拦截器
$http.interceptors.request = (config) => {
    if (!userStore) {
        userStore = useUserStore()
    }

    // 添加token
    if (userStore.token) {
        config.header.Authorization = 'Bearer ' + userStore.token
    }

    return config
}

// 响应拦截器
$http.interceptors.response = (response) => {
    // 处理响应数据
    if (response.statusCode === 401) {
        // token过期，跳转登录
        userStore.logout()
        uni.redirectTo({
            url: '/pages/login/index'
        })
    }
    return response
}

export default $http</div>

    <div class="page-break"></div>
    <h2>5. API接口定义</h2>

    <h3>5.1 任务相关API</h3>
    <div class="file-path">文件路径: src/api/tasks/index.js</div>
    <div class="description">定义任务管理相关的API接口，包括任务列表、任务详情、任务提交等</div>
    <div class="code-block">import request from '@/utils/request'

// api地址
const api = {
    list: '/app-api/tasks/list',
    receive: '/app-api/tasks/receive',
    detail: '/app-api/tasks/detail-v2',
    publishDetail: '/app-api/tasks/publish-detail',
    publishList: '/app-api/tasks/publish-list',
    submit: '/app-api/tasks/points/submit-v2',
    submitPublish: '/app-api/tasks/publish/submit',
    locationPointsList: '/app-api/tasks/location/points-list',
    pointsDetail: '/app-api/tasks/points',
    locationFinish: '/app-api/tasks/location/finish',
    locationVerify: '/app-api/tasks/location/verify',
}

// 获取任务列表
export function getTasksList(params) {
    return request.get(api.list, params)
}

// 接受任务
export function receiveTask(taskId) {
    return request.post(api.receive, { taskId })
}

// 获取任务详情
export function getTaskDetail(taskId) {
    return request.get(api.detail + '/' + taskId)
}

// 提交巡查点
export function submitTaskPoint(data) {
    return request.post(api.submit, data)
}

// 获取巡查点列表
export function getLocationPointsList(params) {
    return request.get(api.locationPointsList, params)
}

// 完成巡查点
export function finishLocation(data) {
    return request.post(api.locationFinish, data)
}</div>

    <h3>5.2 用户相关API</h3>
    <div class="file-path">文件路径: src/api/user/index.js</div>
    <div class="description">定义用户管理相关的API接口</div>
    <div class="code-block">import request from '@/utils/request'

const api = {
    info: '/app-api/user/info',
    list: '/app-api/user/list',
    correctionList: "/app-api/user/correction/list",
    correctionDetail: "/app-api/user/correction/detail",
    delete: "/app-api/user/delete",
    userBadge: "/app-api/user/badge"
}

// 获取用户信息
export function getUserInfo() {
    return request.get(api.info)
}

// 用户列表
export function getUserListByHospitalId(hospitalId) {
    return request.get(api.list + '/' + hospitalId)
}

// 获取整改列表
export function getCorrectionList(params) {
    return request.get(api.correctionList, params)
}

// 删除用户账号
export function deleteAccount() {
    return request.post(api.delete)
}

// 获取用户角标数据
export function getUserBadge() {
    return request.get(api.userBadge)
}</div>

    <h3>5.3 登录注册API</h3>
    <div class="file-path">文件路径: src/api/register/index.js</div>
    <div class="description">定义用户注册相关的API接口</div>
    <div class="code-block">import request from '@/utils/request'

const api = {
  sendSmsCode: '/app-api/getSmsCode',
  register: '/app-api/register'
}

/**
 * 发送短信验证码
 * @param {string} phone - 手机号
 * @returns {Promise}
 */
export function sendSmsCode(phone) {
  return request.get(api.sendSmsCode, { phone })
}

/**
 * 用户注册
 * @param {Object} data - 注册信息
 * @param {string} data.phone - 手机号
 * @param {string} data.code - 验证码
 * @param {string} data.password - 密码
 * @returns {Promise}
 */
export function register(data) {
  return request.post(api.register, data)
}</div>

    <div class="page-break"></div>
    <h2>6. 页面组件</h2>

    <h3>6.1 应用首页</h3>
    <div class="file-path">文件路径: src/pages/index/index.vue</div>
    <div class="description">应用启动页面，负责检查登录状态并跳转到相应页面</div>
    <div class="code-block">&lt;template&gt;
    &lt;view&gt;&lt;/view&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { onLoad } from '@dcloudio/uni-app'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()

onLoad(() => {
    // 检查登录状态
    if (userStore.isLogin) {
        // 已登录，直接跳转到任务列表页
        uni.redirectTo({
            url: '/pages/tasks/list/list'
        })
    } else {
        // 未登录，跳转到登录页
        uni.redirectTo({
            url: '/pages/login/index'
        })
    }
})
&lt;/script&gt;</div>

    <h3>6.2 用户登录页面</h3>
    <div class="file-path">文件路径: src/pages/login/index.vue</div>
    <div class="description">用户登录页面，包含用户名密码输入、记住密码、隐私协议等功能</div>
    <div class="code-block">&lt;template&gt;
  &lt;view class="t-login"&gt;
    &lt;!-- 页面装饰图片 --&gt;
    &lt;image class="img-a" src="@/static/login-head.png"&gt;&lt;/image&gt;
    &lt;image class="img-b" src="@/static/3.png"&gt;&lt;/image&gt;

    &lt;!-- 标题 --&gt;
    &lt;view class="t-b"&gt;{{ title }}&lt;/view&gt;

    &lt;form class="cl"&gt;
      &lt;view class="t-a"&gt;
        &lt;image src="@/static/sj.png"&gt;&lt;/image&gt;
        &lt;input type="text" name="username" placeholder="请输入用户名" v-model="username"/&gt;
      &lt;/view&gt;

      &lt;view class="t-a"&gt;
        &lt;image src="@/static/pwd.png"&gt;&lt;/image&gt;
        &lt;input type="password" name="password" placeholder="请输入密码" v-model="password"/&gt;
      &lt;/view&gt;

      &lt;view class="t-a remember-pwd"&gt;
        &lt;label class="checkbox"&gt;
          &lt;checkbox-group @change="rememberHandler"&gt;
            &lt;checkbox :checked="rememberPassword" value="true"/&gt;
          &lt;/checkbox-group&gt;
          记住密码
        &lt;/label&gt;
      &lt;/view&gt;

      &lt;button @click="login()" :disabled="!agreedPrivacy"&gt;登 录&lt;/button&gt;

      &lt;!-- 隐私协议选择框 --&gt;
      &lt;view class="privacy-agreement"&gt;
        &lt;label class="checkbox"&gt;
          &lt;checkbox-group @change="privacyHandler"&gt;
            &lt;checkbox :checked="agreedPrivacy" value="true"/&gt;
          &lt;/checkbox-group&gt;
          &lt;text&gt;已阅读并同意&lt;/text&gt;
          &lt;text class="privacy-link" @click="showPrivacyPolicy"&gt;《隐私政策》&lt;/text&gt;
        &lt;/label&gt;
      &lt;/view&gt;

      &lt;view class="bottom-area"&gt;
        &lt;view v-if="isRegister" class="register-link" @click="goToRegister"&gt;没有账号？去注册&lt;/view&gt;
      &lt;/view&gt;
    &lt;/form&gt;
  &lt;/view&gt;
&lt;/template&gt;

&lt;script setup&gt;
import {ref, onMounted, getCurrentInstance} from 'vue';
import {useUserStore} from '@/stores/user'
import * as HomeApi from '@/api/home/<USER>'

const store = useUserStore()
const title = ref('欢迎回来！')
const username = ref('')
const password = ref('')
const rememberPassword = ref(false)
const agreedPrivacy = ref(false)
const isRegister = ref(false)

// 登录处理
const login = () => {
  if (!agreedPrivacy.value) {
    uni.showToast({title: '请先阅读并同意隐私协议', icon: 'none'})
    return
  }

  if (!username.value || !password.value) {
    uni.showToast({title: '请输入用户名和密码', icon: 'none'})
    return
  }

  uni.showLoading({ title: '请稍等' })

  // 登陆
  store.login(username.value, password.value).then(data => {
    // 登录成功后处理记住密码
    if (rememberPassword.value) {
      uni.setStorageSync('savedAccount', username.value)
      uni.setStorageSync('savedPassword', password.value)
    }

    uni.showToast({title: '登录成功！', icon: 'none'})
    // 跳转主页
    setTimeout(() => {
      uni.redirectTo({
        url: '/pages/tasks/list/list'
      })
    }, 1500)
  }).catch(err => {
    console.error('登录失败:', err)
  }).finally(() => {
    uni.hideLoading()
  })
}

// 记住密码处理
const rememberHandler = (event) => {
  rememberPassword.value = event.detail.value[0] === 'true'
}

// 隐私协议处理
const privacyHandler = (event) => {
  agreedPrivacy.value = event.detail.value[0] === 'true'
}

// 显示隐私协议
const showPrivacyPolicy = () => {
  uni.navigateTo({
    url: '/pages/contact/privacy'
  })
}

// 跳转注册页面
const goToRegister = () => {
  uni.navigateTo({
    url: '/pages/register/index'
  })
}
&lt;/script&gt;</div>

    <h3>6.3 任务列表页面</h3>
    <div class="file-path">文件路径: src/pages/tasks/list/list.vue</div>
    <div class="description">任务列表页面，展示不同状态的任务，支持下拉刷新和上拉加载</div>
    <div class="code-block">&lt;template&gt;
  &lt;view class="container"&gt;
    &lt;!-- 用户信息 --&gt;
    &lt;userInfo /&gt;

    &lt;!-- Tab 导航部分 --&gt;
    &lt;view class="tabs"&gt;
      &lt;view class="tab" :class="{ active: activeTab === index }"
            v-for="(tab, index) in tabList" :key="index"
            @click="selectTab(index, tab.status)"&gt;
        {{ tab.name }}
        &lt;view class="underline" v-if="activeTab === index"&gt;&lt;/view&gt;
      &lt;/view&gt;
    &lt;/view&gt;

    &lt;!-- 任务列表区域 --&gt;
    &lt;view class="task-list"&gt;
      &lt;!-- 任务卡片列表 --&gt;
      &lt;task-card v-if="list.length > 0" v-for="task in list"
                 :key="task.id" :task-info="task" @accept="handleAccept" /&gt;

      &lt;!-- 空状态 --&gt;
      &lt;view v-else class="empty-state" v-if="!loading"&gt;
        &lt;CustomEmpty mode="list" icon="/static/empty.jpg"
                     title="暂无任务" description="当前没有符合条件的任务" /&gt;
      &lt;/view&gt;

      &lt;!-- 加载状态 --&gt;
      &lt;view class="load-more"&gt;
        &lt;view v-if="loading" class="loading"&gt;
          &lt;text&gt;加载中...&lt;/text&gt;
        &lt;/view&gt;
        &lt;text v-else-if="!hasMore && list.length > 0"&gt;没有更多数据了&lt;/text&gt;
      &lt;/view&gt;
    &lt;/view&gt;

    &lt;!-- 自定义 TabBar --&gt;
    &lt;CustomTabBar ref="customTabBar"/&gt;
  &lt;/view&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { ref, onMounted } from 'vue'
import * as TasksApi from '@/api/tasks'
import { onLoad, onShow, onReachBottom, onPullDownRefresh } from "@dcloudio/uni-app"
import TaskCard from '@/components/taskCard/TaskCard.vue'
import userInfo from "@/components/userInfo/userInfo.vue"
import CustomTabBar from '@/components/CustomTabBar/CustomTabBar.vue'
import CustomEmpty from '@/components/CustomEmpty/CustomEmpty.vue'

// Tab 列表及选中状态
const tabList = [
  { name: "所有", status: -1 },
  { name: "待接受", status: 20 },
  { name: "待执行", status: 30 },
  { name: "进行中", status: 40 },
  { name: "已完成", status: 50 },
]
const activeTab = ref(0)

// 分页相关数据
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const hasMore = ref(true)
const loading = ref(false)
const list = ref([])

onMounted(() => {
  getTasksList(tabList[activeTab.value].status)
})

onShow(() => {
  currentPage.value = 1
  total.value = 0
  getTasksList(tabList[activeTab.value].status)
})

// 获取任务列表
const getTasksList = async (status, isLoadMore = false) => {
  if (loading.value) return

  try {
    loading.value = true
    console.log('加载数据', { page: currentPage.value, status })

    const result = await TasksApi.getTasksList({
      status,
      page: currentPage.value,
      pageSize: pageSize.value
    })

    if (result.code === 200) {
      const newList = result.data.list || []

      if (isLoadMore) {
        list.value = [...list.value, ...newList]
      } else {
        list.value = newList
      }

      total.value = result.data.total || 0
      hasMore.value = list.value.length < total.value
    }
  } catch (error) {
    console.error('获取任务列表失败:', error)
    uni.showToast({
      title: '获取任务列表失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
    uni.stopPullDownRefresh()
  }
}

// 选择Tab
const selectTab = (index, status) => {
  if (activeTab.value === index) return

  activeTab.value = index
  currentPage.value = 1
  hasMore.value = true
  getTasksList(status)
}

// 处理任务接受
const handleAccept = async (taskId) => {
  try {
    uni.showLoading({ title: '处理中...' })

    const result = await TasksApi.receiveTask(taskId)
    if (result.code === 200) {
      uni.showToast({
        title: '接受成功',
        icon: 'success'
      })
      // 刷新列表
      currentPage.value = 1
      getTasksList(tabList[activeTab.value].status)
    }
  } catch (error) {
    console.error('接受任务失败:', error)
    uni.showToast({
      title: '接受失败',
      icon: 'none'
    })
  } finally {
    uni.hideLoading()
  }
}

// 下拉刷新
onPullDownRefresh(() => {
  currentPage.value = 1
  hasMore.value = true
  getTasksList(tabList[activeTab.value].status)
})

// 上拉加载更多
onReachBottom(() => {
  if (hasMore.value && !loading.value) {
    currentPage.value++
    getTasksList(tabList[activeTab.value].status, true)
  }
})
&lt;/script&gt;</div>

    <div class="page-break"></div>
    <h2>7. 自定义组件</h2>

    <h3>7.1 自定义输入框组件</h3>
    <div class="file-path">文件路径: src/components/CustomInput/CustomInput.vue</div>
    <div class="description">封装的自定义输入框组件，支持多种类型、前后缀图标、清除功能等</div>
    <div class="code-block">&lt;template&gt;
  &lt;view class="custom-input-wrapper" :class="{ 'input-disabled': disabled }"&gt;
    &lt;!-- 前缀图标 --&gt;
    &lt;view v-if="prefixIcon" class="input-prefix"&gt;
      &lt;text class="input-icon"&gt;{{ prefixIcon }}&lt;/text&gt;
    &lt;/view&gt;

    &lt;!-- 输入框 --&gt;
    &lt;input
      v-if="!isTextarea"
      class="custom-input"
      :class="[
        `input-${border}`,
        {
          'input-focus': isFocused,
          'input-error': error
        }
      ]"
      :type="type"
      :value="modelValue"
      :placeholder="placeholder"
      :disabled="disabled"
      :maxlength="maxlength > 0 ? maxlength : undefined"
      :readonly="readonly"
      :style="inputStyle"
      @input="handleInput"
      @focus="handleFocus"
      @blur="handleBlur"
      @confirm="handleConfirm"
    /&gt;

    &lt;!-- 多行文本框 --&gt;
    &lt;textarea
      v-else
      class="custom-textarea"
      :class="[
        `input-${border}`,
        {
          'input-focus': isFocused,
          'input-error': error
        }
      ]"
      :value="modelValue"
      :placeholder="placeholder"
      :disabled="disabled"
      :maxlength="maxlength > 0 ? maxlength : undefined"
      :readonly="readonly"
      :style="textareaStyle"
      @input="handleInput"
      @focus="handleFocus"
      @blur="handleBlur"
    /&gt;

    &lt;!-- 后缀图标 --&gt;
    &lt;view v-if="suffixIcon" class="input-suffix" @click="handleSuffixClick"&gt;
      &lt;text class="input-icon"&gt;{{ suffixIcon }}&lt;/text&gt;
    &lt;/view&gt;

    &lt;!-- 清除按钮 --&gt;
    &lt;view v-if="clearable && modelValue && !disabled" class="input-clear" @click="handleClear"&gt;
      &lt;text class="clear-icon"&gt;✕&lt;/text&gt;
    &lt;/view&gt;
  &lt;/view&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { defineProps, defineEmits, computed, ref } from 'vue'

const props = defineProps({
  modelValue: {
    type: [String, Number],
    default: ''
  },
  type: {
    type: String,
    default: 'text' // text, number, password, tel, email
  },
  placeholder: {
    type: String,
    default: '请输入内容'
  },
  disabled: {
    type: Boolean,
    default: false
  },
  readonly: {
    type: Boolean,
    default: false
  },
  clearable: {
    type: Boolean,
    default: false
  },
  maxlength: {
    type: Number,
    default: -1 // -1表示无限制
  },
  prefixIcon: {
    type: String,
    default: ''
  },
  suffixIcon: {
    type: String,
    default: ''
  },
  border: {
    type: String,
    default: 'surround', // surround, bottom, none
    validator: (value) => ['surround', 'bottom', 'none'].includes(value)
  },
  error: {
    type: Boolean,
    default: false
  },
  isTextarea: {
    type: Boolean,
    default: false
  },
  height: {
    type: [String, Number],
    default: ''
  },
  autoHeight: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'focus', 'blur', 'confirm', 'clear', 'suffix-click'])

const isFocused = ref(false)

// 计算样式
const inputStyle = computed(() => {
  const style = {}
  if (props.height) {
    style.height = typeof props.height === 'number' ? `${props.height}rpx` : props.height
  }
  return style
})

const textareaStyle = computed(() => {
  const style = {}
  if (props.height) {
    style.height = typeof props.height === 'number' ? `${props.height}rpx` : props.height
  }
  if (props.autoHeight) {
    style.height = 'auto'
  }
  return style
})

// 事件处理
const handleInput = (event) => {
  emit('update:modelValue', event.detail.value)
}

const handleFocus = (event) => {
  isFocused.value = true
  emit('focus', event)
}

const handleBlur = (event) => {
  isFocused.value = false
  emit('blur', event)
}

const handleConfirm = (event) => {
  emit('confirm', event)
}

const handleClear = () => {
  emit('update:modelValue', '')
  emit('clear')
}

const handleSuffixClick = () => {
  emit('suffix-click')
}
&lt;/script&gt;</div>

    <h3>7.2 任务卡片组件</h3>
    <div class="file-path">文件路径: src/components/taskCard/TaskCard.vue</div>
    <div class="description">任务卡片组件，用于展示任务信息和操作按钮</div>
    <div class="code-block">&lt;template&gt;
  &lt;view class="task-card" @click="goToDetail"&gt;
    &lt;view class="task-header"&gt;
      &lt;view class="task-title"&gt;{{ taskInfo.title }}&lt;/view&gt;
      &lt;view class="task-status" :class="getStatusClass(taskInfo.status)"&gt;
        {{ getStatusText(taskInfo.status) }}
      &lt;/view&gt;
    &lt;/view&gt;

    &lt;view class="task-content"&gt;
      &lt;view class="task-info"&gt;
        &lt;text class="info-label"&gt;任务编号：&lt;/text&gt;
        &lt;text class="info-value"&gt;{{ taskInfo.taskNo }}&lt;/text&gt;
      &lt;/view&gt;

      &lt;view class="task-info"&gt;
        &lt;text class="info-label"&gt;创建时间：&lt;/text&gt;
        &lt;text class="info-value"&gt;{{ formatDate(taskInfo.createTime) }}&lt;/text&gt;
      &lt;/view&gt;

      &lt;view class="task-info" v-if="taskInfo.hospitalName"&gt;
        &lt;text class="info-label"&gt;院区：&lt;/text&gt;
        &lt;text class="info-value"&gt;{{ taskInfo.hospitalName }}&lt;/text&gt;
      &lt;/view&gt;
    &lt;/view&gt;

    &lt;view class="task-actions" v-if="showActions"&gt;
      &lt;button v-if="taskInfo.status === 20"
              class="accept-btn"
              @click.stop="handleAccept"&gt;
        接受任务
      &lt;/button&gt;
    &lt;/view&gt;
  &lt;/view&gt;
&lt;/template&gt;

&lt;script setup&gt;
import { defineProps, defineEmits, computed } from 'vue'
import dayjs from 'dayjs'

const props = defineProps({
  taskInfo: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['accept'])

// 计算是否显示操作按钮
const showActions = computed(() => {
  return props.taskInfo.status === 20 // 待接受状态显示接受按钮
})

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    20: '待接受',
    30: '待执行',
    40: '进行中',
    50: '已完成'
  }
  return statusMap[status] || '未知'
}

// 获取状态样式类
const getStatusClass = (status) => {
  const classMap = {
    20: 'status-pending',
    30: 'status-todo',
    40: 'status-doing',
    50: 'status-done'
  }
  return classMap[status] || ''
}

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return ''
  return dayjs(dateStr).format('YYYY-MM-DD HH:mm')
}

// 跳转到详情页
const goToDetail = () => {
  uni.navigateTo({
    url: `/pages/tasks/detail/detail-v2?id=${props.taskInfo.id}`
  })
}

// 处理接受任务
const handleAccept = () => {
  emit('accept', props.taskInfo.id)
}
&lt;/script&gt;</div>

    <div class="page-break"></div>
    <h2>8. 工具函数</h2>

    <h3>8.1 本地存储工具</h3>
    <div class="file-path">文件路径: src/utils/storage.js</div>
    <div class="description">封装本地存储操作，支持过期时间设置</div>
    <div class="code-block">/**
 * 本地存储工具类
 * 支持设置过期时间
 */
class Storage {
  /**
   * 设置存储
   * @param {string} key 键名
   * @param {any} value 值
   * @param {number} expire 过期时间（秒）
   */
  set(key, value, expire = null) {
    const data = {
      value,
      expire: expire ? Date.now() + expire * 1000 : null
    }

    try {
      uni.setStorageSync(key, JSON.stringify(data))
    } catch (error) {
      console.error('存储设置失败:', error)
    }
  }

  /**
   * 获取存储
   * @param {string} key 键名
   * @param {any} defaultValue 默认值
   * @returns {any}
   */
  get(key, defaultValue = null) {
    try {
      const data = uni.getStorageSync(key)
      if (!data) return defaultValue

      const parsedData = JSON.parse(data)

      // 检查是否过期
      if (parsedData.expire && Date.now() > parsedData.expire) {
        this.remove(key)
        return defaultValue
      }

      return parsedData.value
    } catch (error) {
      console.error('存储获取失败:', error)
      return defaultValue
    }
  }

  /**
   * 删除存储
   * @param {string} key 键名
   */
  remove(key) {
    try {
      uni.removeStorageSync(key)
    } catch (error) {
      console.error('存储删除失败:', error)
    }
  }

  /**
   * 清空所有存储
   */
  clear() {
    try {
      uni.clearStorageSync()
    } catch (error) {
      console.error('存储清空失败:', error)
    }
  }

  /**
   * 检查键是否存在
   * @param {string} key 键名
   * @returns {boolean}
   */
  has(key) {
    return this.get(key) !== null
  }
}

export default new Storage()</div>

    <h3>8.2 日期处理工具</h3>
    <div class="file-path">文件路径: src/utils/date.js</div>
    <div class="description">日期处理相关的工具函数</div>
    <div class="code-block">import dayjs from 'dayjs'

/**
 * 格式化日期
 * @param {string|Date} date 日期
 * @param {string} format 格式
 * @returns {string}
 */
export function formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
  if (!date) return ''
  return dayjs(date).format(format)
}

/**
 * 获取相对时间
 * @param {string|Date} date 日期
 * @returns {string}
 */
export function getRelativeTime(date) {
  if (!date) return ''

  const now = dayjs()
  const target = dayjs(date)
  const diff = now.diff(target, 'minute')

  if (diff < 1) return '刚刚'
  if (diff < 60) return `${diff}分钟前`
  if (diff < 1440) return `${Math.floor(diff / 60)}小时前`
  if (diff < 10080) return `${Math.floor(diff / 1440)}天前`

  return target.format('YYYY-MM-DD')
}

/**
 * 判断是否为今天
 * @param {string|Date} date 日期
 * @returns {boolean}
 */
export function isToday(date) {
  if (!date) return false
  return dayjs(date).isSame(dayjs(), 'day')
}

/**
 * 获取时间戳
 * @param {string|Date} date 日期
 * @returns {number}
 */
export function getTimestamp(date = new Date()) {
  return dayjs(date).valueOf()
}</div>

    <div class="page-break"></div>
    <h2>9. 总结</h2>

    <div class="description">
        <strong>代码特点：</strong>
        <ul>
            <li>采用Vue 3 Composition API，代码结构清晰，逻辑复用性强</li>
            <li>使用uni-app框架，支持多平台部署（H5、微信小程序、APP等）</li>
            <li>采用Pinia进行状态管理，数据流向清晰</li>
            <li>封装了完整的网络请求模块，统一处理请求和响应</li>
            <li>自定义组件设计合理，可复用性强</li>
            <li>工具函数封装完善，提高开发效率</li>
            <li>代码注释详细，便于维护和扩展</li>
        </ul>
    </div>

    <div class="description">
        <strong>主要功能模块：</strong>
        <ul>
            <li>用户认证系统：登录、注册、权限管理</li>
            <li>任务管理系统：任务列表、任务详情、任务操作</li>
            <li>巡查点管理：巡查点列表、质控点管理、数据提交</li>
            <li>个人中心：用户信息管理、系统设置</li>
            <li>推送服务：消息推送、角标管理</li>
        </ul>
    </div>

    <div class="description">
        <strong>技术亮点：</strong>
        <ul>
            <li>响应式设计，适配多种屏幕尺寸</li>
            <li>网络状态检测和错误处理机制</li>
            <li>本地存储管理，支持数据过期机制</li>
            <li>组件化开发，提高代码复用率</li>
            <li>统一的API接口管理</li>
            <li>完善的错误处理和用户提示</li>
        </ul>
    </div>

</body>
</html>

