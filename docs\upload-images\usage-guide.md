# ImageUploader 使用指南

## 快速开始

### 1. 安装和引入

将 `ImageUploader.vue` 组件复制到你的项目中：

```
src/
  components/
    ImageUploader/
      ImageUploader.vue
```

在页面中引入组件：

```vue
<script setup>
import ImageUploader from '@/components/ImageUploader/ImageUploader.vue'
</script>
```

### 2. 基础使用

```vue
<template>
  <ImageUploader v-model="imageList" />
</template>

<script setup>
import { ref } from 'vue'

const imageList = ref([])
</script>
```

## 常见场景

### 场景1: 商品图片上传

```vue
<template>
  <view class="product-form">
    <ImageUploader 
      v-model="productImages"
      title="商品图片"
      :max-count="9"
      :required="true"
      @change="validateImages"
    />
  </view>
</template>

<script setup>
import { ref } from 'vue'

const productImages = ref([])

const validateImages = (images) => {
  if (images.length === 0) {
    uni.showToast({
      title: '请至少上传一张商品图片',
      icon: 'none'
    })
  }
}
</script>
```

### 场景2: 身份证照片上传

```vue
<template>
  <view class="id-card-upload">
    <ImageUploader 
      v-model="idCardImages"
      title="身份证照片"
      :max-count="2"
      :required="true"
      :source-type="['camera']"
      :quality="90"
    />
  </view>
</template>

<script setup>
import { ref } from 'vue'

const idCardImages = ref([])
</script>
```

### 场景3: 头像上传

```vue
<template>
  <view class="avatar-upload">
    <ImageUploader 
      v-model="avatarImages"
      title="头像"
      :max-count="1"
      :size-type="['compressed']"
      :quality="80"
    />
  </view>
</template>

<script setup>
import { ref } from 'vue'

const avatarImages = ref([])
</script>
```

### 场景4: 查看模式

```vue
<template>
  <view class="image-viewer">
    <ImageUploader 
      v-model="viewImages"
      title="查看图片"
      :disabled="true"
      :show-tip="false"
    />
  </view>
</template>

<script setup>
import { ref } from 'vue'

const viewImages = ref([
  { url: 'https://example.com/image1.jpg', status: 'success' },
  { url: 'https://example.com/image2.jpg', status: 'success' }
])
</script>
```

## 表单集成

### 与表单验证集成

```vue
<template>
  <view class="form-container">
    <form @submit="handleSubmit">
      <!-- 其他表单项 -->
      <view class="form-item">
        <ImageUploader 
          v-model="formData.images"
          title="产品图片"
          :max-count="6"
          :required="true"
          @change="validateForm"
        />
        <text v-if="errors.images" class="error-text">{{ errors.images }}</text>
      </view>
      
      <button form-type="submit" class="submit-btn">提交</button>
    </form>
  </view>
</template>

<script setup>
import { ref, reactive } from 'vue'

const formData = reactive({
  images: [],
  // 其他表单数据
})

const errors = reactive({
  images: ''
})

const validateForm = () => {
  // 清除之前的错误
  errors.images = ''
  
  // 验证图片
  if (formData.images.length === 0) {
    errors.images = '请至少上传一张图片'
    return false
  }
  
  // 检查是否有上传中的图片
  const uploadingImages = formData.images.filter(img => img.status === 'uploading')
  if (uploadingImages.length > 0) {
    errors.images = '请等待图片上传完成'
    return false
  }
  
  // 检查是否有上传失败的图片
  const errorImages = formData.images.filter(img => img.status === 'error')
  if (errorImages.length > 0) {
    errors.images = '存在上传失败的图片，请重新上传'
    return false
  }
  
  return true
}

const handleSubmit = () => {
  if (!validateForm()) {
    return
  }
  
  // 提取图片URL
  const imageUrls = formData.images
    .filter(img => img.status === 'success')
    .map(img => img.url)
  
  console.log('提交的图片URL:', imageUrls)
  
  // 提交表单数据
  // ...
}
</script>
```

## 数据处理

### 获取图片URL列表

```javascript
// 从组件数据中提取URL
const getImageUrls = (imageList) => {
  return imageList
    .filter(item => item.status === 'success' && item.url)
    .map(item => item.url)
}

// 使用示例
const imageUrls = getImageUrls(imageList.value)
console.log('图片URL列表:', imageUrls)
```

### 预设图片数据

```javascript
// 从服务器数据转换为组件格式
const convertServerData = (serverImages) => {
  return serverImages.map(url => ({
    url: url,
    status: 'success'
  }))
}

// 使用示例
const serverData = ['https://example.com/1.jpg', 'https://example.com/2.jpg']
const imageList = ref(convertServerData(serverData))
```

### 监听上传进度

```vue
<template>
  <ImageUploader 
    v-model="imageList"
    @upload-success="onUploadSuccess"
    @upload-error="onUploadError"
  />
</template>

<script setup>
const onUploadSuccess = ({ index, url, item }) => {
  console.log(`第${index + 1}张图片上传成功:`, url)
  
  // 可以在这里做一些额外处理
  // 比如更新数据库、发送通知等
}

const onUploadError = ({ index, error, item }) => {
  console.error(`第${index + 1}张图片上传失败:`, error)
  
  // 可以在这里做错误处理
  // 比如记录日志、重试上传等
}
</script>
```

## 样式定制

### 修改主题色

```css
/* 全局样式文件中 */
.image-uploader .add-btn {
  border-color: #ff6b6b !important;
}

.image-uploader .add-icon {
  color: #ff6b6b !important;
}

.image-uploader .delete-btn {
  background-color: #ff6b6b !important;
}
```

### 自定义尺寸

```css
.image-uploader .image-item,
.image-uploader .add-btn {
  width: 150rpx !important;
  height: 150rpx !important;
}
```

### 修改布局

```css
.image-uploader .image-list {
  justify-content: center !important;
  gap: 15rpx !important;
}
```

## 性能优化

### 1. 图片压缩

```vue
<ImageUploader 
  :quality="60"
  :size-type="['compressed']"
/>
```

### 2. 限制文件大小

在上传API中添加文件大小检查：

```javascript
// 在 uploadFile 方法中
export function uploadFile(path) {
  return new Promise((resolve, reject) => {
    uni.getFileInfo({
      filePath: path,
      success: (res) => {
        // 限制文件大小为5MB
        if (res.size > 5 * 1024 * 1024) {
          reject(new Error('文件大小不能超过5MB'))
          return
        }
        
        // 继续上传
        // ...
      }
    })
  })
}
```

### 3. 懒加载

对于大量图片的场景，可以考虑实现懒加载：

```vue
<image 
  :src="item.url" 
  lazy-load
  mode="aspectFill"
/>
```

## 常见问题

### Q: 如何限制图片格式？

A: 可以在选择图片后进行格式检查：

```javascript
const chooseImage = () => {
  uni.chooseImage({
    success: (res) => {
      const validFiles = res.tempFilePaths.filter(path => {
        const ext = path.split('.').pop().toLowerCase()
        return ['jpg', 'jpeg', 'png', 'gif'].includes(ext)
      })
      
      if (validFiles.length !== res.tempFilePaths.length) {
        uni.showToast({
          title: '只支持jpg、png、gif格式',
          icon: 'none'
        })
      }
      
      uploadImages(validFiles)
    }
  })
}
```

### Q: 如何实现图片裁剪？

A: 可以在选择图片后调用裁剪API：

```javascript
const chooseImage = () => {
  uni.chooseImage({
    count: 1,
    success: (res) => {
      // 调用图片裁剪
      uni.navigateTo({
        url: `/pages/crop/crop?src=${encodeURIComponent(res.tempFilePaths[0])}`
      })
    }
  })
}
```

### Q: 如何批量删除图片？

A: 可以添加批量操作功能：

```vue
<template>
  <view>
    <button @click="toggleBatchMode">批量删除</button>
    <ImageUploader 
      v-model="imageList"
      :disabled="batchMode"
    />
    <button v-if="batchMode" @click="batchDelete">删除选中</button>
  </view>
</template>
```

## 更多示例

完整的演示代码请参考 `demo.vue` 文件，其中包含了各种使用场景的完整示例。
