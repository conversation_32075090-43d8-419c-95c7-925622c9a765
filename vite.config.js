import { defineConfig, loadEnv } from 'vite'
import uni from '@dcloudio/vite-plugin-uni'
import { resolve } from 'path'

// 获取环境变量
export default defineConfig(({ mode }) => {
	// 加载指定模式下的环境变量
	const env = loadEnv(mode, process.cwd())  // 使用 process.cwd() 来加载环境变量
	// 打印环境变量，确保其正确加载
	console.log(">>>>>>应用运行的模式: ", process.env.NODE_ENV)  // 环境模式 (development, production ...) CLI自带的功参数
	console.log('>>>>>>请求地址:', env.VITE_API_BASE_URL)  // 自定义环境变量

	return {
		plugins: [uni()],
		resolve: {
			alias: {
				'@': resolve(__dirname, 'src'),  // 设置别名
			}
		},
		server: {
			port: 5183,
			proxy: {
				'^/app-api': {
					target: env.VITE_API_BASE_URL, // 后端服务器地址
					changeOrigin: true, // 改变源，防止后端服务器拒绝请求
					rewrite: (path) => path.replace(/^\/app-api/, '/app-api'), // 重写路径
				},
				'^/uploads': {
					target: env.VITE_API_BASE_URL, // 后端服务器地址
					changeOrigin: true, // 改变源，防止后端服务器拒绝请求
					rewrite: (path) => path.replace(/^\/uploads/, '/uploads'), // 重写路径
				}
			}
		},
	}
})
