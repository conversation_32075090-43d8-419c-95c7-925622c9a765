<template>
  <view class="title"> {{ title }}</view>
  <scroll-view class="location-list" scroll-x="true" :show-scrollbar="false" :scroll-into-view="scrollItemId" :scroll-with-animation="true" ref="scrollView">
    <view class="location-wrapper" v-for="(location, index) in locations" :key="index" :id="'location-' + index">
      <view class="location-item"
            :class="{
                      'bg-done': location.status === 20,
                      'bg-notdone': location.status === 10,
                    }"
            @click="onLocationClick(location,index)">
        <view class="location-content">
          {{ location.locationName }}
        </view>
        <view class="status-badge" :class="getStatusClass(location.status)">
          {{ getStatusText(location.status) }}
        </view>
      </view>
      <!-- 将指示器移到外层 -->
      <view class="indicator" v-if="currentIndex === index">
        <view class="triangle"></view>
      </view>
    </view>
  </scroll-view>
</template>

<script setup>
import { ref, watch, onMounted, nextTick } from 'vue';

// Props
const props = defineProps({
  // 查询点列表
  locations: {
    type: Array,
    required: true,
    default: () => [],
  },
  // 当前选中的是第几个
  currentIndex: {
    type: Number,
    default: -1,
  },
  // 顶部标题
  title: {
    type: String,
    default: "巡查地点列表：(可左右滑动)"
  }
})

// 滚动相关
const scrollView = ref(null);
const scrollItemId = ref('');

// 监听 currentIndex 变化，自动滚动到对应的位置
watch(() => props.currentIndex, (newIndex) => {
  if (newIndex >= 0 && props.locations && props.locations.length > 0) {
    // 使用 nextTick 确保 DOM 更新后再滚动
    nextTick(() => {
      scrollToLocation(newIndex);
    });
  }
}, { immediate: true });

// 监听 locations 变化，当数据加载完成后检查是否需要滚动
watch(() => props.locations, () => {
  if (props.currentIndex >= 0 && props.locations && props.locations.length > 0) {
    // 使用 nextTick 确保 DOM 更新后再滚动
    nextTick(() => {
      scrollToLocation(props.currentIndex);
    });
  }
}, { immediate: true });

// 在组件挂载后初始化滚动
onMounted(() => {
  if (props.currentIndex >= 0 && props.locations && props.locations.length > 0) {
    // 延迟一点执行，确保组件完全渲染
    setTimeout(() => {
      scrollToLocation(props.currentIndex);
    }, 300);
  }
});

// 滚动到指定位置
const scrollToLocation = (index) => {
  console.log('滚动到位置:', index);
  scrollItemId.value = `location-${index}`;
};

// Emits
const emits = defineEmits(['indexChange']);

// 点击事件处理
const onLocationClick = (location, index) => {
  emits('indexChange', index);
};

const getStatusText = (status) => {
  const statusMap = {
    10: '待', // 待巡查
    15: '进', // 进行中
    20: '完', // 已完成
    30: '异', // 异常
    40: '跳'  // 已跳过
  }
  return statusMap[status] || ''
}

const getStatusClass = (status) => {
  return {
    'status-pending': status === 10,    // 待巡查
    'status-processing': status === 15,  // 进行中
    'status-completed': status === 20,   // 已完成
    'status-abnormal': status === 30,    // 异常
    'status-skipped': status === 40      // 已跳过
  }
}
</script>

<style scoped>
.title {
  color: #777777;
  margin-top: 10rpx;
}

.location-list {
  display: flex;
  overflow-x: auto;
  white-space: nowrap;
  padding: 8px 0;
  margin-top: 10rpx;
}

/* 添加包装器样式 */
.location-wrapper {
  position: relative;
  display: inline-block;
  padding-bottom: 12px; /* 为三角形预留空间 */
  margin-right: 8px;
}

/* 修改location-item样式 */
.location-item {
  position: relative;
  padding: 16px 20px;
  background-color: #fff;
  border: 1px solid #ddd;
  color: #333;
  border-radius: 4px;
  font-size: 14px;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
}

.location-item:active {
  transform: scale(0.95);
}

.bg-act {
  background: #7db3f2;
}

.bg-done {
  background: #7db342;
}

.bg-notdone {
  background: #e3e3e3;
}

.bg-error{
  background: #ce8a80;
}

.status-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 24px;  /* 稍微调大一点 */
  height: 24px; /* 稍微调大一点 */
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #fff;
  z-index: 1;  /* 确保角标在最上层 */
}

.status-pending {
  background-color: #909399; /* 待巡查-灰色 */
}

.status-processing {
  background-color: #409EFF; /* 进行中-蓝色 */
}

.status-completed {
  background-color: #67C23A; /* 已完成-绿色 */
}

.status-abnormal {
  background-color: #F56C6C; /* 异常-红色 */
}

.status-skipped {
  background-color: #E6A23C; /* 已跳过-橙色 */
}

/* 修改指示器样式 */
.indicator {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 16px;
  height: 8px;
  display: flex;
  justify-content: center;
  pointer-events: none;
}

.triangle {
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-bottom: 8px solid #007AFF; /* 改为 border-bottom，使三角形向上 */
  border-top: none; /* 移除 border-top */
}

/* 移除未使用的样式 */
.location-active {
  display: none;
}
</style>
