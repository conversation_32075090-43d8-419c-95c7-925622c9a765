<template>
  <view class="custom-empty" :style="emptyStyle">
    <!-- 图标/图片 -->
    <view class="empty-image">
      <image 
        v-if="icon" 
        :src="icon" 
        class="empty-icon"
        mode="aspectFit"
        @error="handleImageError"
      />
      <view v-else class="empty-default-icon">
        <text class="default-icon-text">{{ getDefaultIcon() }}</text>
      </view>
    </view>
    
    <!-- 文字描述 -->
    <view class="empty-text">
      <view class="empty-title" v-if="title">
        {{ title }}
      </view>
      <view class="empty-description" v-if="description">
        {{ description }}
      </view>
    </view>
    
    <!-- 操作按钮 -->
    <view class="empty-action" v-if="showAction">
      <slot name="action">
        <button 
          v-if="actionText" 
          class="empty-btn"
          :class="{ 'btn-disabled': actionDisabled }"
          :disabled="actionDisabled"
          @click="handleAction"
        >
          {{ actionText }}
        </button>
      </slot>
    </view>
  </view>
</template>

<script setup>
import { defineProps, defineEmits, computed } from 'vue'

const props = defineProps({
  mode: {
    type: String,
    default: 'data', // data, list, search, network, permission, cart
    validator: (value) => ['data', 'list', 'search', 'network', 'permission', 'cart'].includes(value)
  },
  icon: {
    type: String,
    default: ''
  },
  title: {
    type: String,
    default: ''
  },
  description: {
    type: String,
    default: ''
  },
  actionText: {
    type: String,
    default: ''
  },
  actionDisabled: {
    type: Boolean,
    default: false
  },
  showAction: {
    type: Boolean,
    default: true
  },
  width: {
    type: String,
    default: '100%'
  },
  height: {
    type: String,
    default: 'auto'
  },
  marginTop: {
    type: String,
    default: '100rpx'
  }
})

const emit = defineEmits(['action', 'image-error'])

// 容器样式
const emptyStyle = computed(() => ({
  width: props.width,
  height: props.height,
  marginTop: props.marginTop
}))

// 获取默认图标
const getDefaultIcon = () => {
  const iconMap = {
    data: '📄',
    list: '📋',
    search: '🔍',
    network: '🌐',
    permission: '🔒',
    cart: '🛒'
  }
  return iconMap[props.mode] || '📄'
}

// 获取默认标题
const getDefaultTitle = () => {
  const titleMap = {
    data: '暂无数据',
    list: '列表为空',
    search: '搜索无结果',
    network: '网络异常',
    permission: '暂无权限',
    cart: '购物车为空'
  }
  return titleMap[props.mode] || '暂无数据'
}

// 获取默认描述
const getDefaultDescription = () => {
  const descMap = {
    data: '当前没有可显示的数据',
    list: '当前列表没有任何内容',
    search: '试试其他关键词吧',
    network: '请检查网络连接后重试',
    permission: '您没有访问权限',
    cart: '快去添加一些商品吧'
  }
  return descMap[props.mode] || '当前没有可显示的数据'
}

// 处理操作按钮点击
const handleAction = () => {
  if (!props.actionDisabled) {
    emit('action')
  }
}

// 处理图片加载错误
const handleImageError = (error) => {
  emit('image-error', error)
}

// 如果没有提供标题和描述，使用默认值
const displayTitle = computed(() => props.title || getDefaultTitle())
const displayDescription = computed(() => props.description || getDefaultDescription())
</script>

<style scoped>
.custom-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 40rpx;
  text-align: center;
  box-sizing: border-box;
}

/* 图标/图片区域 */
.empty-image {
  margin-bottom: 40rpx;
}

.empty-icon {
  width: 200rpx;
  height: 200rpx;
  opacity: 0.6;
}

.empty-default-icon {
  width: 200rpx;
  height: 200rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  border-radius: 16rpx;
  opacity: 0.6;
}

.default-icon-text {
  font-size: 80rpx;
}

/* 文字区域 */
.empty-text {
  margin-bottom: 40rpx;
}

.empty-title {
  font-size: 32rpx;
  color: #000;
  font-weight: 500;
  margin-bottom: 16rpx;
  line-height: 1.4;
}

.empty-description {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  max-width: 400rpx;
}

/* 操作按钮 */
.empty-action {
  margin-top: 20rpx;
}

.empty-btn {
  padding: 20rpx 40rpx;
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 200rpx;
}

.empty-btn:active {
  background: #096dd9;
  transform: translateY(2rpx);
}

.btn-disabled {
  background: #d9d9d9;
  color: #999;
  cursor: not-allowed;
}

.btn-disabled:active {
  background: #d9d9d9;
  transform: none;
}

/* 不同模式的样式调整 */
.custom-empty[data-mode="network"] .empty-title {
  color: #ff4d4f;
}

.custom-empty[data-mode="permission"] .empty-title {
  color: #faad14;
}

/* 响应式优化 */
@media (max-width: 750rpx) {
  .custom-empty {
    padding: 80rpx 40rpx;
  }
  
  .empty-icon {
    width: 240rpx;
    height: 240rpx;
  }
  
  .empty-default-icon {
    width: 240rpx;
    height: 240rpx;
  }
  
  .default-icon-text {
    font-size: 100rpx;
  }
  
  .empty-title {
    font-size: 34rpx;
  }
  
  .empty-description {
    font-size: 28rpx;
  }
  
  .empty-btn {
    font-size: 30rpx;
    padding: 24rpx 48rpx;
  }
}

/* 动画效果 */
.custom-empty {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(40rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
