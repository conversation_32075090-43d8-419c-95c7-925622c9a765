<template>
  <view class="t-register">
    <!-- 页面装饰图片 -->
    <image class="img-a" src="@/static/login-head.png"></image>
    <image class="img-b" src="@/static/3.png"></image>
    <!-- 标题 -->
    <view class="t-b">{{ title }}</view>
    <form class="cl">
      <!-- 新增院区选择器 -->
      <view class="t-a code-input">
        <image src="@/static/hospital.png" class="icon"></image>
        <picker class="hospital-picker " :range="hospitalList" range-key="name" @change="onHospitalChange">
          <view class="picker-value" :class="{'placeholder': !selectedHospital}">
            {{ selectedHospital ? selectedHospital.name : '请选择所在院区' }}
            <text class="picker-arrow">▼</text>
          </view>
        </picker>
      </view>
      <view class="tip">如果所在院区不存在，请
        <text class="tip-text" @click="goToContact">联系我们</text>
      </view>

      <view class="t-a">
        <image src="@/static/sj.png"></image>
        <input type="number" name="phone" placeholder="请输入手机号" v-model="phone" maxlength="11"/>
      </view>
      <view class="t-a code-input">
        <image src="@/static/pwd.png"></image>
        <input type="number" name="code "placeholder="请输入验证码" v-model="code" maxlength="6"/>
        <view class="code-btn" :class="{ disabled: codeBtnDisabled }" @click="getVerifyCode">
          {{ codeBtnText }}
        </view>
      </view>
      <view class="t-a">
        <image src="@/static/pwd.png"></image>
        <input type="password" name="password" placeholder="请设置密码" v-model="password"/>
      </view>
      <view class="t-a">
        <image src="@/static/pwd.png"></image>
        <input type="password" name="confirmPassword" placeholder="请确认密码" v-model="confirmPassword"/>
      </view>
      <button class="register-btn" @click="register">注 册</button>
      <view class="login-link" @click="goToLogin">已有账号？去登录</view>
    </form>
  </view>
</template>

<script setup>
import {ref, computed, onUnmounted} from 'vue'
import * as Api from '@/api/register/index'
import * as HospitalApi from '@/api/hospital/index'

const title = ref('欢迎注册');
const phone = ref('');
const code = ref('');
const password = ref('');
const confirmPassword = ref('');
const countdown = ref(0);
let timer = null

// 模拟院区数据
const hospitalList =ref([])
const getHospitalList = () => {
  HospitalApi.list().then().then(res => {
    hospitalList.value = res.data.list
  })
}
getHospitalList()

// 选中的院区
const selectedHospital = ref(null);

// 院区选择事件处理
const onHospitalChange = (e) => {
  const index = e.detail.value;
  selectedHospital.value = hospitalList.value[index];
  console.log('选择的院区:', selectedHospital.value);
}

// 验证码按钮文本
const codeBtnText = computed(() => {
  return countdown.value > 0 ? `${countdown.value}秒后重新获取` : '获取验证码';
})

// 验证码按钮是否禁用
const codeBtnDisabled = computed(() => {
  return countdown.value > 0 || !isValidPhone(phone.value);
})

// 验证手机号格式
const isValidPhone = (phone) => {
  return /^1[0-9]\d{9}$/.test(phone);
}

// 获取验证码
const getVerifyCode = () => {
  console.log('点击获取验证码按钮')

  // 如果按钮禁用，直接返回
  if (codeBtnDisabled.value) {
    console.log('按钮已禁用，不执行操作')
    return
  }

  if (!isValidPhone(phone.value)) {
    uni.showToast({
      title: '请输入正确的手机号',
      icon: 'none'
    })
    return
  }

  uni.showLoading({
    title: '发送中'
  })

  console.log('开始发送验证码')

  // 调用模拟的发送验证码API
  Api.sendSmsCode(phone.value)
      .then(res => {
        console.log('验证码发送响应:', res)
        uni.showToast({
          title: '验证码已发送',
          icon: 'success'
        });
        // 开始倒计时
        startCountdown()
      })
      .catch(err => {
        console.error('验证码发送错误:', err)
        uni.showToast({
          title: '发送失败，请稍后重试',
          icon: 'none'
        })
      })
      .finally(() => {
        uni.hideLoading()
      })
}

// 开始倒计时
const startCountdown = () => {
  console.log('开始倒计时')
  countdown.value = 60
  clearInterval(timer)
  timer = setInterval(() => {
    countdown.value--
    console.log('倒计时:', countdown.value)
    if (countdown.value <= 0) {
      clearInterval(timer)
    }
  }, 1000)
}

// 注册
const register = () => {
  // 表单验证
  if (!selectedHospital.value) {
    uni.showToast({
      title: '请选择所在院区',
      icon: 'none'
    });
    return;
  }

  if (!isValidPhone(phone.value)) {
    uni.showToast({
      title: '请输入正确的手机号',
      icon: 'none'
    });
    return;
  }

  if (!code.value || code.value.length !== 6) {
    uni.showToast({
      title: '请输入6位验证码',
      icon: 'none'
    });
    return;
  }

  if (!password.value || password.value.length < 6) {
    uni.showToast({
      title: '密码不能少于6位',
      icon: 'none'
    });
    return;
  }

  if (password.value !== confirmPassword.value) {
    uni.showToast({
      title: '两次密码输入不一致',
      icon: 'none'
    });
    return;
  }

  // 显示加载中
  uni.showLoading({
    title: '注册中',
    mask: true
  });

  // 调用模拟注册API
  Api.register({
    hospitalId: selectedHospital.value.id,
    hospitalName: selectedHospital.value.name,
    phone: phone.value,
    code: code.value,
    password: password.value
  }).then(res => {
    uni.showToast({
      title: '注册成功',
      icon: 'success'
    })

    // 注册成功后跳转到登录页
    setTimeout(() => {
      goToLogin()
    }, 1500)
  }).finally(() => {
    uni.hideLoading()
  })
}


// 跳转到登录页
const goToLogin = () => {
  uni.redirectTo({
    url: '/pages/login/index'
  })
}

// 联系我们
const goToContact = () => {
  uni.navigateTo({
    url: '/pages/contact/index'
  })
}


// 组件卸载时清除定时器
onUnmounted(() => {
  if (timer) {
    clearInterval(timer)
    timer = null
  }
})
</script>

<style>
uni-page-body {
  width: 100%;
  height: 100vh;
  overflow: hidden;
  position: relative;
}

.t-register {
  width: 600rpx;
  height: 100vh;
  margin: 0 auto;
  font-size: 28rpx;
  color: #000;
  position: relative;
}

.img-a {
  position: relative;
  width: 100%;
  height: 130rpx;
  top: 100rpx;
  right: 0;
}

.img-b {
  position: absolute;
  width: 50%;
  left: -150rpx;
  bottom: -150rpx;
}

.t-b {
  text-align: left;
  font-size: 46rpx;
  color: #000;
  padding: 180rpx 0 100rpx 0;
  font-weight: bold;
}

.t-register button.register-btn {
  font-size: 28rpx;
  background: #5677fc;
  color: #fff;
  height: 90rpx;
  line-height: 90rpx;
  border-radius: 50rpx;
  box-shadow: 0 5px 7px 0 rgba(86, 119, 252, 0.2);
  margin-top: 60rpx;
}

.t-register input {
  padding: 0 20rpx 0 120rpx;
  height: 90rpx;
  line-height: 90rpx;
  margin-bottom: 50rpx;
  background: #f8f7fc;
  border: 1px solid #e9e9e9;
  font-size: 28rpx;
  border-radius: 50rpx;
}

.t-register .t-a {
  position: relative;
}

.t-register .t-a image {
  width: 40rpx;
  height: 40rpx;
  position: absolute;
  left: 40rpx;
  top: 28rpx;
  border-right: 2rpx solid #dedede;
  padding-right: 20rpx;
  z-index: 1;
}

.hospital-picker {
  width: 100%;
  height: 90rpx;
  background: #f8f7fc;
  border: 1px solid #e9e9e9;
  border-radius: 50rpx;
  padding-left: 120rpx;
  box-sizing: border-box;
}

.picker-value {
  height: 90rpx;
  line-height: 90rpx;
  font-size: 28rpx;
  color: #333;
  position: relative;
  padding: 0 20rpx 0 0; /* 右侧添加一些内边距，避免文字与箭头重叠 */
  box-sizing: border-box;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 添加选择前的占位符样式 */
.picker-value.placeholder {
  color: #999; /* 未选择时的提示文字颜色，与input的placeholder颜色一致 */
}

.picker-arrow {
  font-size: 20rpx;
  color: #999;
  margin-left: 6rpx;
  position: absolute;
  right: 30rpx;
  top: 50%;
  transform: translateY(-50%);
}

.code-input {
  display: flex;
  align-items: center;
}

.code-input input {
  flex: 1;
  padding-right: 220rpx;
}

.code-btn {
  position: absolute !important;
  right: 10rpx;
  top: 10rpx;
  width: 200rpx !important;
  height: 70rpx !important;
  line-height: 70rpx !important;
  font-size: 24rpx !important;
  background: #f0f2fd !important;
  color: #5677fc !important;
  border-radius: 35rpx !important;
  box-shadow: none !important;
  z-index: 10;
  text-align: center;
}

.code-btn.disabled {
  background: #f5f5f5 !important;
  color: #999 !important;
}

.login-link {
  text-align: center;
  margin-top: 40rpx;
  color: #5677fc;
  font-size: 28rpx;
}

.cl {
  zoom: 1;
}

.cl:after {
  clear: both;
  display: block;
  visibility: hidden;
  height: 0;
  content: '\20';
}


.tip {
  height: 80rpx;
  line-height: 80rpx;
  color: rgba(0, 0, 0, .5);
  padding-left: 20rpx;
  margin-bottom: 10rpx;

}

.tip-text {
  color: rgb(41, 121, 255);
}

</style> 