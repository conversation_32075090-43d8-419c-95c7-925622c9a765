<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>主管巡查系统软件源代码</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.2;
            margin: 20px;
            background: white;
        }
        .code-block {
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            font-size: 11px;
            line-height: 1.1;
            margin: 0;
            padding: 0;
            border: none;
            background: white;
        }
        .page-break {
            page-break-before: always;
        }
        h1, h2, h3 {
            font-size: 14px;
            margin: 10px 0 5px 0;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>主管巡查系统软件源代码</h1>
    
    <div class="code-block">{
    "name" : "主管巡查",
    "appid" : "__UNI__120B055",
    "description" : "",
    "versionName" : "1.2.1",
    "versionCode" : 129,
    "transformPx" : false,
    "app-plus" : {
        "compatible" : {
            "ignoreVersion" : true
        },
        "usingComponents" : true,
        "nvueStyleCompiler" : "uni-app",
        "compilerVersion" : 3,
        "splashscreen" : {
            "alwaysShowBeforeRender" : false,
            "waiting" : false,
            "autoclose" : true,
            "delay" : 0
        },
        "modules" : {
            "Camera" : {},
            "Push" : {}
        },
        "distribute" : {
            "android" : {
                "permissions" : [
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\" />",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\" />",
                    "<uses-permission android:name=\"android.permission.VIBRATE\" />",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\" />",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\" />",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\" />",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\" />",
                    "<uses-permission android:name=\"android.permission.CAMERA\" />",
                    "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\" />",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\" />",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\" />",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\" />",
                    "<uses-permission android:name=\"android.permission.FLASHLIGHT\" />",
                    "<uses-feature android:name=\"android.hardware.camera\" />",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\" />"
                ]
            },
            "ios" : {},
            "sdkConfigs" : {}
        }
    },
    "quickapp" : {},
    "mp-weixin" : {
        "appid" : "wx1234567890abcdef",
        "setting" : {
            "urlCheck" : false
        },
        "usingComponents" : true
    },
    "mp-alipay" : {
        "usingComponents" : true
    },
    "mp-baidu" : {
        "usingComponents" : true
    },
    "mp-toutiao" : {
        "usingComponents" : true
    },
    "uniStatistics" : {
        "enable" : false
    },
    "vueVersion" : "3"
}</div>

    <div class="code-block">{
  "pages": [
    {
      "path": "pages/index/index",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/login/index",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/tasks/list/list",
      "style": {
        "navigationBarTitleText": "任务列表",
        "navigationStyle": "default",
        "enablePullDownRefresh": true
      }
    },
    {
      "path": "pages/tasks/detail/detail-v2",
      "style": {
        "navigationBarTitleText": "任务详情",
        "navigationStyle": "default"
      }
    },
    {
      "path": "pages/tasks/publish/index",
      "style": {
        "navigationBarTitleText": "发布",
        "navigationStyle": "default"
      }
    },
    {
      "path": "pages/tasks/publish/list",
      "style": {
        "navigationBarTitleText": "整改列表",
        "navigationStyle": "default",
        "enablePullDownRefresh": true
      }
    },
    {
      "path": "pages/mine/index",
      "style": {
        "navigationBarTitleText": "我的",
        "navigationStyle": "default"
      }
    },
    {
      "path": "pages/register/index",
      "style": {
        "navigationBarTitleText": "注册",
        "navigationStyle": "default"
      }
    },
    {
      "path": "pages/contact/privacy",
      "style": {
        "navigationBarTitleText": "隐私政策",
        "navigationStyle": "default"
      }
    }
  ],
  "globalStyle": {
    "pageOrientation": "portrait",
    "navigationBarTextStyle": "black",
    "navigationBarTitleText": "主管巡查系统",
    "navigationBarBackgroundColor": "#F8F8F8",
    "backgroundColor": "#F8F8F8"
  },
  "uniIdRouter": {},
  "condition": {
    "current": 0,
    "list": [
      {
        "name": "",
        "path": "",
        "query": ""
      }
    ]
  }
}</div>

    <div class="code-block">import {
    createSSRApp
} from "vue"
import App from "./App.vue"
import * as Pinia from "pinia"
import pushService from '@/utils/push'

console.log(">>>>>>应用运行的模式: ",  process.env.NODE_ENV)
console.log('>>>>>>请求地址:', import.meta.env.VITE_API_BASE_URL)

export function createApp() {
    const app = createSSRApp(App)
    app.use(Pinia.createPinia())
    
    app.config.globalProperties.$push = pushService
    
    return {
        app,
        Pinia,
    }
}</div>

    <div class="code-block"><script setup>
import { onLaunch, onShow, onHide } from '@dcloudio/uni-app'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()

onLaunch(() => {
  console.log('App Launch')
  userStore.initUserInfo()
})

onShow(() => {
  console.log('App Show')
})

onHide(() => {
  console.log('App Hide')
})
</script>

<style>
@import url("@/static/css/common.css");

page {
  background-color: #f5f5f5;
}
</style></div>

    <div class="code-block">import { defineStore } from 'pinia'
import * as HomeApi from '@/api/home/<USER>'
import storage from '@/utils/storage'

export const useUserStore = defineStore('user', {
  state: () => ({
    token: '',
    userInfo: null,
    isLogin: false
  }),

  getters: {
    getUserInfo: (state) => state.userInfo,
    getToken: (state) => state.token,
    getIsLogin: (state) => state.isLogin
  },

  actions: {
    setToken(token) {
      this.token = token
      this.isLogin = !!token
      if (token) {
        storage.set('token', token, 7 * 24 * 60 * 60)
      } else {
        storage.remove('token')
      }
    },

    setUserInfo(userInfo) {
      this.userInfo = userInfo
      if (userInfo) {
        storage.set('userInfo', userInfo, 7 * 24 * 60 * 60)
      } else {
        storage.remove('userInfo')
      }
    },

    async login(username, password) {
      try {
        const response = await HomeApi.login({ username, password })
        if (response.code === 200) {
          const { token, user } = response.data
          this.setToken(token)
          this.setUserInfo(user)
          return response.data
        } else {
          throw new Error(response.message || '登录失败')
        }
      } catch (error) {
        console.error('登录失败:', error)
        uni.showToast({
          title: error.message || '登录失败',
          icon: 'none'
        })
        throw error
      }
    },

    async logout() {
      try {
        await HomeApi.logout()
      } catch (error) {
        console.error('退出登录失败:', error)
      } finally {
        this.setToken('')
        this.setUserInfo(null)
        uni.redirectTo({
          url: '/pages/login/index'
        })
      }
    },

    initUserInfo() {
      const token = storage.get('token')
      const userInfo = storage.get('userInfo')
      
      if (token && userInfo) {
        this.token = token
        this.userInfo = userInfo
        this.isLogin = true
      }
    },

    async refreshUserInfo() {
      try {
        const response = await HomeApi.getUserInfo()
        if (response.code === 200) {
          this.setUserInfo(response.data)
        }
      } catch (error) {
        console.error('刷新用户信息失败:', error)
      }
    }
  }
})</div>

    <div class="code-block">import axios from 'axios'
import { useUserStore } from '@/stores/user'

const request = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

request.interceptors.request.use(
  config => {
    const userStore = useUserStore()
    const token = userStore.getToken

    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }

    if (config.load !== false) {
      uni.showLoading({
        title: '加载中...',
        mask: true
      })
    }

    return config
  },
  error => {
    uni.hideLoading()
    return Promise.reject(error)
  }
)

request.interceptors.response.use(
  response => {
    uni.hideLoading()

    const { code, data, message } = response.data

    if (code === 200) {
      return response.data
    } else if (code === 401) {
      const userStore = useUserStore()
      userStore.logout()
      uni.showToast({
        title: '登录已过期，请重新登录',
        icon: 'none'
      })
      return Promise.reject(new Error('登录已过期'))
    } else {
      uni.showToast({
        title: message || '请求失败',
        icon: 'none'
      })
      return Promise.reject(new Error(message || '请求失败'))
    }
  },
  error => {
    uni.hideLoading()

    let message = '网络错误'
    if (error.response) {
      message = error.response.data?.message || `请求失败 ${error.response.status}`
    } else if (error.request) {
      message = '网络连接失败'
    }

    uni.showToast({
      title: message,
      icon: 'none'
    })

    return Promise.reject(error)
  }
)

export default {
  get(url, params = {}, config = {}) {
    return request.get(url, { params, ...config })
  },

  post(url, data = {}, config = {}) {
    return request.post(url, data, config)
  },

  put(url, data = {}, config = {}) {
    return request.put(url, data, config)
  },

  delete(url, config = {}) {
    return request.delete(url, config)
  }
}</div>

    <div class="code-block">import {func} from 'uview-plus/libs/function/test'
import request from '@/utils/request'

const api = {
    list: '/app-api/tasks/list',
    receive: '/app-api/tasks/receive',
    detail: '/app-api/tasks/detail-v2',
    publishDetail: '/app-api/tasks/publish-detail',
    publishList: '/app-api/tasks/publish-list',
    submit: '/app-api/tasks/points/submit-v2',
    submitPublish: '/app-api/tasks/publish/submit',
    locationPointsList: '/app-api/tasks/location/points-list',
    pointsDetail: '/app-api/tasks/points',
    locationFinish: '/app-api/tasks/location/finish',
    locationVerify: '/app-api/tasks/location/verify',
}

export function getTasksList(params = {}) {
    const requestParams = {
        page: params.page || 1,
        pageSize: params.pageSize || 10
    }

    if (params.status !== -1) {
        requestParams.status = params.status
    }

    return request.get(api.list, params, {load: false})
}

export function receive(tasksId) {
    return request.post(api.receive, {tasksId: tasksId})
}

export function detail(tasksId) {
    return request.get(api.detail, {tasksId: tasksId})
}

export function publishDetail(tasksId) {
    return request.get(api.publishDetail, {tasksId: tasksId})
}

export function publishList() {
    return request.get(api.publishList, null, {loading: false})
}

export function submit(data) {
    return request.post(api.submit, data)
}

export function submitPublish(data) {
    return request.post(api.submitPublish, data)
}

export function getPointsListByLocationID(tasksLocationId) {
    return request.get(api.locationPointsList, {tasksLocationId: tasksLocationId})
}

export function getPointsDetail(id) {
    return request.get(api.pointsDetail, {taskPointsId: id})
}

export function locationFinish(id) {
    return request.post(api.locationFinish, {id: id})
}

export function locationVerify(data) {
    return request.post(api.locationVerify, data)
}</div>

    <div class="code-block">import request from '@/utils/request'

const api = {
    info: '/app-api/user/info',
    list: '/app-api/user/list',
    correctionList: "/app-api/user/correction/list",
    correctionDetail: "/app-api/user/correction/detail",
    delete: "/app-api/user/delete",
    userBadge: "/app-api/user/badge"
}

export function getUserInfo() {
    return request.get(api.info)
}

export function getUserListByHospitalId(hospitalId) {
    return request.get(api.list + '/' + hospitalId)
}

export function getCorrectionList(params) {
    return request.get(api.correctionList, params)
}

export function deleteAccount() {
    return request.post(api.delete)
}

export function getUserBadge() {
    return request.get(api.userBadge)
}</div>

    <div class="code-block">import request from '@/utils/request'

const api = {
    login: '/app-api/auth/login',
    logout: '/app-api/auth/logout',
    register: '/app-api/auth/register',
    getUserInfo: '/app-api/user/info'
}

export function login(data) {
    return request.post(api.login, data)
}

export function logout() {
    return request.post(api.logout)
}

export function register(data) {
    return request.post(api.register, data)
}

export function getUserInfo() {
    return request.get(api.getUserInfo)
}</div>

    <div class="code-block"><template>
	<view></view>
</template>

<script setup>
import { onLoad } from '@dcloudio/uni-app'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()

onLoad(() => {
	if (userStore.isLogin) {
		uni.redirectTo({
			url: '/pages/tasks/list/list'
		})
	} else {
		uni.redirectTo({
			url: '/pages/login/index'
		})
	}
})
</script>

<style>
</style></div>

    <div class="code-block"><template>
  <view class="t-login">
    <image class="img-a" src="@/static/login-head.png"></image>
    <image class="img-b" src="@/static/3.png"></image>
    <view class="t-b">{{ title }}</view>
    <form class="cl">
      <view class="t-a">
        <image src="@/static/sj.png"></image>
        <input type="text" name="username" placeholder="请输入用户名" v-model="username"/>
      </view>
      <view class="t-a">
        <image src="@/static/pwd.png"></image>
        <input type="password" name="password" placeholder="请输入密码" v-model="password"/>
      </view>
      <view class="t-a remember-pwd">
        <label class="checkbox">
          <checkbox-group @change="rememberHandler">
            <checkbox :checked="rememberPassword" value="true"/>
          </checkbox-group>
          记住密码
        </label>
      </view>
      <button @click="login()" :disabled="!agreedPrivacy">登 录</button>
      <view class="privacy-agreement">
        <label class="checkbox">
          <checkbox-group @change="privacyHandler">
            <checkbox :checked="agreedPrivacy" value="true"/>
          </checkbox-group>
          <text>已阅读并同意</text>
          <text class="privacy-link" @click="showPrivacyPolicy">《隐私政策》</text>
        </label>
      </view>
      <view class="bottom-area">
        <view v-if="isRegister" class="register-link" @click="goToRegister">没有账号？去注册</view>
      </view>
    </form>
  </view>
</template>

<script setup>
import {ref, onMounted, getCurrentInstance} from 'vue';
import {useUserStore} from '@/stores/user'
import * as HomeApi from '@/api/home/<USER>'

const store = useUserStore()
const title = ref('欢迎回来！')
const username = ref('')
const password = ref('')
const rememberPassword = ref(false)
const agreedPrivacy = ref(false)
const isRegister = ref(false)
const hasNetworkPermission = ref(true)
let disabled = false

onMounted(() => {
  const savedAccount = uni.getStorageSync('savedAccount')
  const savedPassword = uni.getStorageSync('savedPassword')

  if (savedAccount && savedPassword) {
    username.value = savedAccount
    password.value = savedPassword
    rememberPassword.value = true
  }
})

const login = () => {
  if (disabled) return

  if (!agreedPrivacy.value) {
    uni.showToast({title: '请先阅读并同意隐私协议', icon: 'none'})
    return
  }

  if (!username.value || !password.value) {
    uni.showToast({title: '请输入用户名和密码', icon: 'none'})
    return
  }

  uni.getNetworkType({
    success: (res) => {
      if (res.networkType !== 'none') {
        hasNetworkPermission.value = true

        uni.showLoading({
          title: '请稍等',
          mask: false
        })
        disabled = true

        store.login(username.value, password.value).then(data => {
          if (rememberPassword.value) {
            uni.setStorageSync('savedAccount', username.value)
            uni.setStorageSync('savedPassword', password.value)
          } else {
            uni.removeStorageSync('savedAccount')
            uni.removeStorageSync('savedPassword')
          }

          uni.showToast({title: '登录成功！', icon: 'none'})
          loginSuccess()
        }).catch(err => {
          disabled = false
        }).finally(() => {
          uni.hideLoading()
        })
      } else {
        hasNetworkPermission.value = false
        showNetworkSettingDialog(6)
      }
    },
    fail: () => {
      hasNetworkPermission.value = false
      showNetworkSettingDialog(7)
    }
  })
}

const loginSuccess = () => {
  setTimeout(() => {
    uni.redirectTo({
      url: '/pages/tasks/list/list'
    })
  }, 1500)
}

const rememberHandler = (event) => {
  rememberPassword.value = event.detail.value[0] === 'true'
}

const privacyHandler = (event) => {
  agreedPrivacy.value = event.detail.value[0] === 'true'
}

const showPrivacyPolicy = () => {
  uni.navigateTo({
    url: '/pages/contact/privacy'
  })
}

const goToRegister = () => {
  uni.navigateTo({
    url: '/pages/register/index'
  })
}

const showNetworkSettingDialog = (type) => {
  uni.showModal({
    title: '网络设置',
    content: '当前网络不可用，请检查网络设置',
    confirmText: '去设置',
    success: (res) => {
      if (res.confirm) {
        uni.openSetting()
      }
    }
  })
}
</script>

<style>
uni-page-body {
  width: 100%;
  height: 100vh;
  overflow: hidden;
  position: relative;
}

.t-login {
  width: 600rpx;
  height: 100vh;
  margin: 0 auto;
  font-size: 28rpx;
  color: #000;
  position: relative;
}

.img-a {
  position: relative;
  width: 100%;
  height: 130rpx;
  top: 140rpx;
  right: 0;
  margin-bottom: 200rpx;
}

.img-b {
  position: absolute;
  width: 100%;
  height: 100vh;
  top: 0;
  left: 0;
  z-index: -1;
}

.t-b {
  font-size: 48rpx;
  font-weight: bold;
  text-align: center;
  margin-bottom: 80rpx;
  color: #333;
}

.cl {
  padding: 0 40rpx;
}

.t-a {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50rpx;
  margin-bottom: 30rpx;
  padding: 0 30rpx;
  height: 90rpx;
}

.t-a image {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
}

.t-a input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.remember-pwd {
  justify-content: flex-start;
  background: transparent;
  height: auto;
  padding: 0;
  margin-bottom: 40rpx;
}

.checkbox {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #666;
}

button {
  width: 100%;
  height: 90rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 50rpx;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
}

button:disabled {
  background: #ccc;
}

.privacy-agreement {
  display: flex;
  justify-content: center;
  margin-bottom: 40rpx;
}

.privacy-agreement .checkbox {
  font-size: 22rpx;
  color: #999;
}

.privacy-link {
  color: #007aff;
  text-decoration: underline;
}

.bottom-area {
  text-align: center;
}

.register-link {
  color: #007aff;
  font-size: 26rpx;
}
</style></div>

    <div class="code-block"><template>
  <view class="container">
    <userInfo />

    <view class="tabs">
      <view class="tab" :class="{ active: activeTab === index }"
            v-for="(tab, index) in tabList" :key="index"
            @click="selectTab(index, tab.status)">
        {{ tab.name }}
        <view class="underline" v-if="activeTab === index"></view>
      </view>
    </view>

    <view class="task-list">
      <task-card v-if="list.length > 0" v-for="task in list"
                 :key="task.id" :task-info="task" @accept="handleAccept" />

      <view v-else class="empty-state" v-if="!loading">
        <CustomEmpty mode="list" icon="/static/empty.jpg" title="暂无任务" description="当前没有符合条件的任务" />
      </view>

      <view class="load-more">
        <view v-if="loading" class="loading">
          <text>加载中...</text>
        </view>
        <text v-else-if="!hasMore && list.length > 0">没有更多数据了</text>
      </view>
    </view>

    <CustomTabBar ref="customTabBar"/>
  </view>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance } from 'vue'
import * as TasksApi from '@/api/tasks'
import { onLoad, onShow, onReachBottom, onPullDownRefresh } from "@dcloudio/uni-app"
import TaskCard from '@/components/taskCard/TaskCard.vue'
import userInfo from "@/components/userInfo/userInfo.vue"
import CustomTabBar from '@/components/CustomTabBar/CustomTabBar.vue'
import CustomEmpty from '@/components/CustomEmpty/CustomEmpty.vue'
import tabBarManager from '@/utils/tabBar'

const tabList = [
  { name: "所有", status: -1 },
  { name: "待接受", status: 20 },
  { name: "待执行", status: 30 },
  { name: "进行中", status: 40 },
  { name: "已完成", status: 50 },
]
const activeTab = ref(0)

const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const hasMore = ref(true)
const loading = ref(false)
const list = ref([])

onMounted(() => {
  getTasksList(tabList[activeTab.value].status)
  tabBarManager.init()
})

const updateTabBarIndex = () => {
  const tabBarComponent = getCurrentInstance()?.refs?.customTabBar
  if (tabBarComponent && tabBarComponent.setCurrentIndex) {
    tabBarComponent.setCurrentIndex(0)
  }
}

onShow(() => {
  currentPage.value = 1
  total.value = 0
  getTasksList(tabList[activeTab.value].status)
  updateTabBarIndex()
})

const getTasksList = async (status, isLoadMore = false) => {
  if (loading.value) return

  try {
    loading.value = true
    console.log('加载数据', { page: currentPage.value, status })

    const result = await TasksApi.getTasksList({
      status,
      page: currentPage.value,
      pageSize: pageSize.value
    })

    if (result.code === 200) {
      const { list: newList, total: totalCount } = result.data

      if (isLoadMore) {
        list.value = [...list.value, ...newList]
      } else {
        list.value = newList
      }

      total.value = totalCount
      hasMore.value = list.value.length < totalCount

      console.log('数据加载完成', {
        currentLength: list.value.length,
        total: totalCount,
        hasMore: hasMore.value
      })

      await tabBarManager.refreshBadgeData()
    }
  } catch (error) {
    console.error('获取任务列表失败:', error)
    uni.showToast({
      title: '获取数据失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

const selectTab = (index, status) => {
  if (activeTab.value === index) return

  activeTab.value = index
  currentPage.value = 1
  hasMore.value = true
  getTasksList(status)
}

const handleAccept = async (taskId) => {
  try {
    uni.showLoading({ title: '处理中...' })

    const result = await TasksApi.receive(taskId)
    if (result.code === 200) {
      uni.showToast({
        title: '接受成功',
        icon: 'success'
      })
      currentPage.value = 1
      getTasksList(tabList[activeTab.value].status)
    }
  } catch (error) {
    console.error('接受任务失败:', error)
    uni.showToast({
      title: '接受失败',
      icon: 'none'
    })
  } finally {
    uni.hideLoading()
  }
}

onPullDownRefresh(() => {
  currentPage.value = 1
  hasMore.value = true
  getTasksList(tabList[activeTab.value].status)
  uni.stopPullDownRefresh()
})

onReachBottom(() => {
  if (hasMore.value && !loading.value) {
    currentPage.value++
    getTasksList(tabList[activeTab.value].status, true)
  }
})
</script>

<style scoped>
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 120rpx;
}

.tabs {
  display: flex;
  background: white;
  padding: 0 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.tab {
  flex: 1;
  text-align: center;
  padding: 30rpx 0;
  font-size: 28rpx;
  color: #666;
  position: relative;
  cursor: pointer;
}

.tab.active {
  color: #007aff;
  font-weight: bold;
}

.underline {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background: #007aff;
  border-radius: 2rpx;
}

.task-list {
  padding: 20rpx;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
  color: #999;
}

.load-more {
  text-align: center;
  padding: 30rpx 0;
  color: #999;
  font-size: 24rpx;
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
}
</style></div>

    <div class="code-block"><template>
  <view class="task-card" @click="goToDetail">
    <view class="task-header">
      <view class="task-title">{{ taskInfo.title }}</view>
      <view class="task-status" :class="getStatusClass(taskInfo.status)">
        {{ getStatusText(taskInfo.status) }}
      </view>
    </view>

    <view class="task-content">
      <view class="task-info">
        <text class="info-label">任务编号：</text>
        <text class="info-value">{{ taskInfo.taskNo }}</text>
      </view>

      <view class="task-info">
        <text class="info-label">创建时间：</text>
        <text class="info-value">{{ formatDate(taskInfo.createTime) }}</text>
      </view>

      <view class="task-info" v-if="taskInfo.hospitalName">
        <text class="info-label">院区：</text>
        <text class="info-value">{{ taskInfo.hospitalName }}</text>
      </view>
    </view>

    <view class="task-actions" v-if="showActions">
      <button v-if="taskInfo.status === 20"
              class="accept-btn"
              @click.stop="handleAccept">
        接受任务
      </button>
    </view>
  </view>
</template>

<script setup>
import { defineProps, defineEmits, computed } from 'vue'
import dayjs from 'dayjs'

const props = defineProps({
  taskInfo: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['accept'])

const showActions = computed(() => {
  return props.taskInfo.status === 20
})

const getStatusText = (status) => {
  const statusMap = {
    20: '待接受',
    30: '待执行',
    40: '进行中',
    50: '已完成'
  }
  return statusMap[status] || '未知'
}

const getStatusClass = (status) => {
  const classMap = {
    20: 'status-pending',
    30: 'status-todo',
    40: 'status-doing',
    50: 'status-done'
  }
  return classMap[status] || ''
}

const formatDate = (dateStr) => {
  if (!dateStr) return ''
  return dayjs(dateStr).format('YYYY-MM-DD HH:mm')
}

const goToDetail = () => {
  uni.navigateTo({
    url: `/pages/tasks/detail/detail-v2?id=${props.taskInfo.id}`
  })
}

const handleAccept = () => {
  emit('accept', props.taskInfo.id)
}
</script>

<style scoped>
.task-card {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.task-title {
  flex: 1;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.4;
  margin-right: 20rpx;
}

.task-status {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  font-weight: 500;
  white-space: nowrap;
}

.status-pending {
  background: #fff7e6;
  color: #fa8c16;
}

.status-todo {
  background: #e6f7ff;
  color: #1890ff;
}

.status-doing {
  background: #f6ffed;
  color: #52c41a;
}

.status-done {
  background: #f0f0f0;
  color: #666;
}

.task-content {
  margin-bottom: 20rpx;
}

.task-info {
  display: flex;
  margin-bottom: 12rpx;
  font-size: 26rpx;
}

.info-label {
  color: #999;
  min-width: 140rpx;
}

.info-value {
  color: #333;
  flex: 1;
}

.task-actions {
  display: flex;
  justify-content: flex-end;
}

.accept-btn {
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 16rpx 32rpx;
  font-size: 26rpx;
}
</style></div>

    <div class="code-block"><template>
  <view class="custom-input-wrapper" :class="{ 'input-disabled': disabled }">
    <view v-if="prefixIcon" class="input-prefix">
      <text class="input-icon">{{ prefixIcon }}</text>
    </view>

    <input
      v-if="!isTextarea"
      class="custom-input"
      :class="[
        `input-${border}`,
        {
          'input-focus': isFocused,
          'input-error': error
        }
      ]"
      :type="type"
      :value="modelValue"
      :placeholder="placeholder"
      :disabled="disabled"
      :maxlength="maxlength > 0 ? maxlength : undefined"
      :readonly="readonly"
      :style="inputStyle"
      @input="handleInput"
      @focus="handleFocus"
      @blur="handleBlur"
      @confirm="handleConfirm"
    />

    <textarea
      v-else
      class="custom-textarea"
      :class="[
        `input-${border}`,
        {
          'input-focus': isFocused,
          'input-error': error
        }
      ]"
      :value="modelValue"
      :placeholder="placeholder"
      :disabled="disabled"
      :maxlength="maxlength > 0 ? maxlength : undefined"
      :readonly="readonly"
      :style="textareaStyle"
      @input="handleInput"
      @focus="handleFocus"
      @blur="handleBlur"
    />

    <view v-if="suffixIcon" class="input-suffix" @click="handleSuffixClick">
      <text class="input-icon">{{ suffixIcon }}</text>
    </view>

    <view v-if="clearable && modelValue && !disabled" class="input-clear" @click="handleClear">
      <text class="clear-icon">✕</text>
    </view>
  </view>
</template>

<script setup>
import { defineProps, defineEmits, computed, ref } from 'vue'

const props = defineProps({
  modelValue: {
    type: [String, Number],
    default: ''
  },
  type: {
    type: String,
    default: 'text'
  },
  placeholder: {
    type: String,
    default: '请输入内容'
  },
  disabled: {
    type: Boolean,
    default: false
  },
  readonly: {
    type: Boolean,
    default: false
  },
  clearable: {
    type: Boolean,
    default: false
  },
  maxlength: {
    type: Number,
    default: -1
  },
  prefixIcon: {
    type: String,
    default: ''
  },
  suffixIcon: {
    type: String,
    default: ''
  },
  border: {
    type: String,
    default: 'surround',
    validator: (value) => ['surround', 'bottom', 'none'].includes(value)
  },
  error: {
    type: Boolean,
    default: false
  },
  isTextarea: {
    type: Boolean,
    default: false
  },
  height: {
    type: [String, Number],
    default: ''
  },
  autoHeight: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'focus', 'blur', 'confirm', 'clear', 'suffix-click'])

const isFocused = ref(false)

const inputStyle = computed(() => {
  const style = {}
  if (props.height) {
    style.height = typeof props.height === 'number' ? `${props.height}rpx` : props.height
  }
  return style
})

const textareaStyle = computed(() => {
  const style = {}
  if (props.height) {
    style.height = typeof props.height === 'number' ? `${props.height}rpx` : props.height
  }
  if (props.autoHeight) {
    style.height = 'auto'
  }
  return style
})

const handleInput = (event) => {
  emit('update:modelValue', event.detail.value)
}

const handleFocus = (event) => {
  isFocused.value = true
  emit('focus', event)
}

const handleBlur = (event) => {
  isFocused.value = false
  emit('blur', event)
}

const handleConfirm = (event) => {
  emit('confirm', event)
}

const handleClear = () => {
  emit('update:modelValue', '')
  emit('clear')
}

const handleSuffixClick = () => {
  emit('suffix-click')
}
</script>

<style scoped>
.custom-input-wrapper {
  display: flex;
  align-items: center;
  position: relative;
  background: white;
  transition: all 0.3s ease;
}

.custom-input,
.custom-textarea {
  flex: 1;
  padding: 20rpx 24rpx;
  font-size: 28rpx;
  color: #000;
  background: transparent;
  border: none;
  outline: none;
  box-sizing: border-box;
  line-height: 1.4;
}

.custom-textarea {
  resize: none;
  min-height: 120rpx;
  padding: 20rpx 24rpx;
}

.input-surround {
  border: 2rpx solid #d9d9d9;
  border-radius: 8rpx;
}

.input-bottom {
  border-bottom: 2rpx solid #d9d9d9;
}

.input-none {
  border: none;
}

.input-focus.input-surround {
  border-color: #1890ff;
  box-shadow: 0 0 0 4rpx rgba(24, 144, 255, 0.1);
}

.input-focus.input-bottom {
  border-bottom-color: #1890ff;
}

.input-error.input-surround {
  border-color: #ff4d4f;
}

.input-error.input-bottom {
  border-bottom-color: #ff4d4f;
}

.input-disabled {
  background: #f5f5f5;
  cursor: not-allowed;
}

.input-disabled .custom-input,
.input-disabled .custom-textarea {
  color: #999;
  cursor: not-allowed;
}

.input-prefix,
.input-suffix {
  padding: 0 16rpx;
  color: #999;
  font-size: 28rpx;
}

.input-suffix {
  cursor: pointer;
}

.input-clear {
  padding: 0 16rpx;
  color: #ccc;
  cursor: pointer;
  font-size: 24rpx;
}

.input-clear:hover {
  color: #999;
}
</style></div>

    <div class="code-block"><template>
  <view class="custom-tab-bar">
    <view
      v-for="(item, index) in tabList"
      :key="index"
      class="tab-item"
      :class="{ active: currentIndex === index }"
      @click="switchTab(index)"
    >
      <view class="tab-icon-wrapper">
        <image
          :src="currentIndex === index ? item.selectedIconPath : item.iconPath"
          class="tab-icon"
        />
        <view
          v-if="item.badge && item.badge > 0"
          class="badge"
          :class="{ 'badge-dot': item.badge === true }"
        >
          <text v-if="item.badge !== true" class="badge-text">
            {{ item.badge > 99 ? '99+' : item.badge }}
          </text>
        </view>
      </view>
      <text class="tab-text" :class="{ active: currentIndex === index }">
        {{ item.text }}
      </text>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, defineExpose } from 'vue'
import { useTabBarStore } from '@/stores/tabBar'

const tabBarStore = useTabBarStore()
const currentIndex = ref(0)

const tabList = computed(() => [
  {
    pagePath: "pages/tasks/list/list",
    text: "巡查",
    iconPath: "/static/tabbar/task.png",
    selectedIconPath: "/static/tabbar/task-active.png",
    badge: tabBarStore.taskBadge
  },
  {
    pagePath: "pages/tasks/publish/list",
    text: "整改",
    iconPath: "/static/tabbar/publish-list.png",
    selectedIconPath: "/static/tabbar/publish-list-active.png",
    badge: tabBarStore.publishBadge
  },
  {
    pagePath: "pages/tasks/publish/index",
    text: "发布",
    iconPath: "/static/tabbar/publish.png",
    selectedIconPath: "/static/tabbar/publish-active.png",
    badge: tabBarStore.createBadge
  },
  {
    pagePath: "pages/mine/index",
    text: "我的",
    iconPath: "/static/tabbar/mine.png",
    selectedIconPath: "/static/tabbar/mine-active.png",
    badge: tabBarStore.mineBadge
  }
])

const switchTab = (index) => {
  if (currentIndex.value === index) return

  currentIndex.value = index
  const targetPage = tabList.value[index]

  uni.switchTab({
    url: `/${targetPage.pagePath}`,
    fail: () => {
      uni.redirectTo({
        url: `/${targetPage.pagePath}`
      })
    }
  })
}

const setCurrentIndex = (index) => {
  currentIndex.value = index
}

defineExpose({
  setCurrentIndex
})
</script>

<style scoped>
.custom-tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background: white;
  display: flex;
  border-top: 1rpx solid #e5e5e5;
  z-index: 1000;
  padding-bottom: env(safe-area-inset-bottom);
}

.tab-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 10rpx 0;
  position: relative;
}

.tab-icon-wrapper {
  position: relative;
  margin-bottom: 8rpx;
}

.tab-icon {
  width: 48rpx;
  height: 48rpx;
}

.badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  min-width: 32rpx;
  height: 32rpx;
  background: #ff4d4f;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx solid white;
}

.badge-dot {
  width: 16rpx;
  height: 16rpx;
  min-width: 16rpx;
  border-radius: 8rpx;
}

.badge-text {
  color: white;
  font-size: 20rpx;
  font-weight: bold;
  line-height: 1;
  padding: 0 6rpx;
}

.tab-text {
  font-size: 20rpx;
  color: #999;
  line-height: 1;
}

.tab-text.active {
  color: #007aff;
}
</style></div>

    <div class="code-block">class Storage {
  set(key, value, expire = null) {
    const data = {
      value,
      expire: expire ? Date.now() + expire * 1000 : null
    }

    try {
      uni.setStorageSync(key, JSON.stringify(data))
    } catch (error) {
      console.error('存储设置失败:', error)
    }
  }

  get(key, defaultValue = null) {
    try {
      const data = uni.getStorageSync(key)
      if (!data) return defaultValue

      const parsedData = JSON.parse(data)

      if (parsedData.expire && Date.now() > parsedData.expire) {
        this.remove(key)
        return defaultValue
      }

      return parsedData.value
    } catch (error) {
      console.error('存储获取失败:', error)
      return defaultValue
    }
  }

  remove(key) {
    try {
      uni.removeStorageSync(key)
    } catch (error) {
      console.error('存储删除失败:', error)
    }
  }

  clear() {
    try {
      uni.clearStorageSync()
    } catch (error) {
      console.error('存储清空失败:', error)
    }
  }

  has(key) {
    return this.get(key) !== null
  }
}

export default new Storage()</div>

    <div class="code-block">import dayjs from 'dayjs'

export function formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
  if (!date) return ''
  return dayjs(date).format(format)
}

export function getRelativeTime(date) {
  if (!date) return ''

  const now = dayjs()
  const target = dayjs(date)
  const diff = now.diff(target, 'minute')

  if (diff < 1) return '刚刚'
  if (diff < 60) return `${diff}分钟前`
  if (diff < 1440) return `${Math.floor(diff / 60)}小时前`
  if (diff < 10080) return `${Math.floor(diff / 1440)}天前`

  return target.format('YYYY-MM-DD')
}

export function isToday(date) {
  if (!date) return false
  return dayjs(date).isSame(dayjs(), 'day')
}

export function getTimestamp(date = new Date()) {
  return dayjs(date).valueOf()
}

export function formatDuration(seconds) {
  if (!seconds || seconds < 0) return '0秒'

  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60

  if (hours > 0) {
    return `${hours}小时${minutes}分钟`
  } else if (minutes > 0) {
    return `${minutes}分钟${secs}秒`
  } else {
    return `${secs}秒`
  }
}

export function getDateRange(type = 'week') {
  const now = dayjs()
  let start, end

  switch (type) {
    case 'today':
      start = now.startOf('day')
      end = now.endOf('day')
      break
    case 'week':
      start = now.startOf('week')
      end = now.endOf('week')
      break
    case 'month':
      start = now.startOf('month')
      end = now.endOf('month')
      break
    case 'year':
      start = now.startOf('year')
      end = now.endOf('year')
      break
    default:
      start = now.startOf('day')
      end = now.endOf('day')
  }

  return {
    start: start.format('YYYY-MM-DD HH:mm:ss'),
    end: end.format('YYYY-MM-DD HH:mm:ss')
  }
}</div>

    <div class="code-block"><template>
  <view class="task-detail-container">
    <view class="detail-header">
      <view class="task-title">{{ taskDetail.title }}</view>
      <view class="task-status" :class="getStatusClass(taskDetail.status)">
        {{ getStatusText(taskDetail.status) }}
      </view>
    </view>

    <view class="detail-content">
      <view class="info-section">
        <view class="section-title">基本信息</view>
        <view class="info-item">
          <text class="info-label">任务编号：</text>
          <text class="info-value">{{ taskDetail.taskNo }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">创建时间：</text>
          <text class="info-value">{{ formatDate(taskDetail.createTime) }}</text>
        </view>
        <view class="info-item" v-if="taskDetail.hospitalName">
          <text class="info-label">院区：</text>
          <text class="info-value">{{ taskDetail.hospitalName }}</text>
        </view>
        <view class="info-item" v-if="taskDetail.description">
          <text class="info-label">任务描述：</text>
          <text class="info-value">{{ taskDetail.description }}</text>
        </view>
      </view>

      <view class="location-section" v-if="taskDetail.locations && taskDetail.locations.length > 0">
        <view class="section-title">巡查点位</view>
        <view class="location-list">
          <view
            v-for="location in taskDetail.locations"
            :key="location.id"
            class="location-item"
            @click="goToLocationDetail(location)"
          >
            <view class="location-info">
              <view class="location-name">{{ location.name }}</view>
              <view class="location-desc" v-if="location.description">
                {{ location.description }}
              </view>
            </view>
            <view class="location-status" :class="getLocationStatusClass(location.status)">
              {{ getLocationStatusText(location.status) }}
            </view>
          </view>
        </view>
      </view>

      <view class="images-section" v-if="taskDetail.images && taskDetail.images.length > 0">
        <view class="section-title">相关图片</view>
        <view class="images-grid">
          <image
            v-for="(image, index) in taskDetail.images"
            :key="index"
            :src="image"
            class="detail-image"
            mode="aspectFill"
            @click="previewImage(index)"
          />
        </view>
      </view>
    </view>

    <view class="detail-actions" v-if="showActions">
      <button
        v-if="taskDetail.status === 20"
        class="action-btn primary"
        @click="acceptTask"
      >
        接受任务
      </button>
      <button
        v-if="taskDetail.status === 30"
        class="action-btn primary"
        @click="startTask"
      >
        开始执行
      </button>
      <button
        v-if="taskDetail.status === 40"
        class="action-btn success"
        @click="completeTask"
      >
        完成任务
      </button>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import * as TasksApi from '@/api/tasks'
import dayjs from 'dayjs'

const taskId = ref('')
const taskDetail = ref({})
const loading = ref(false)

const showActions = computed(() => {
  return [20, 30, 40].includes(taskDetail.value.status)
})

onLoad((options) => {
  if (options.id) {
    taskId.value = options.id
    getTaskDetail()
  }
})

const getTaskDetail = async () => {
  if (!taskId.value) return

  try {
    loading.value = true
    const result = await TasksApi.detail(taskId.value)

    if (result.code === 200) {
      taskDetail.value = result.data
    }
  } catch (error) {
    console.error('获取任务详情失败:', error)
    uni.showToast({
      title: '获取详情失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

const getStatusText = (status) => {
  const statusMap = {
    20: '待接受',
    30: '待执行',
    40: '进行中',
    50: '已完成'
  }
  return statusMap[status] || '未知'
}

const getStatusClass = (status) => {
  const classMap = {
    20: 'status-pending',
    30: 'status-todo',
    40: 'status-doing',
    50: 'status-done'
  }
  return classMap[status] || ''
}

const getLocationStatusText = (status) => {
  const statusMap = {
    0: '未开始',
    1: '进行中',
    2: '已完成'
  }
  return statusMap[status] || '未知'
}

const getLocationStatusClass = (status) => {
  const classMap = {
    0: 'location-pending',
    1: 'location-doing',
    2: 'location-done'
  }
  return classMap[status] || ''
}

const formatDate = (dateStr) => {
  if (!dateStr) return ''
  return dayjs(dateStr).format('YYYY-MM-DD HH:mm')
}

const acceptTask = async () => {
  try {
    uni.showLoading({ title: '处理中...' })

    const result = await TasksApi.receive(taskId.value)
    if (result.code === 200) {
      uni.showToast({
        title: '接受成功',
        icon: 'success'
      })
      getTaskDetail()
    }
  } catch (error) {
    console.error('接受任务失败:', error)
  } finally {
    uni.hideLoading()
  }
}

const startTask = () => {
  if (taskDetail.value.locations && taskDetail.value.locations.length > 0) {
    const firstLocation = taskDetail.value.locations[0]
    goToLocationDetail(firstLocation)
  }
}

const completeTask = async () => {
  uni.showModal({
    title: '确认完成',
    content: '确定要完成这个任务吗？',
    success: async (res) => {
      if (res.confirm) {
        try {
          uni.showLoading({ title: '处理中...' })

          const result = await TasksApi.submit({
            taskId: taskId.value,
            status: 50
          })

          if (result.code === 200) {
            uni.showToast({
              title: '任务完成',
              icon: 'success'
            })
            getTaskDetail()
          }
        } catch (error) {
          console.error('完成任务失败:', error)
        } finally {
          uni.hideLoading()
        }
      }
    }
  })
}

const goToLocationDetail = (location) => {
  uni.navigateTo({
    url: `/pages/tasks/location/detail?taskId=${taskId.value}&locationId=${location.id}`
  })
}

const previewImage = (index) => {
  uni.previewImage({
    urls: taskDetail.value.images,
    current: index
  })
}
</script>

<style scoped>
.task-detail-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 120rpx;
}

.detail-header {
  background: white;
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  border-bottom: 1rpx solid #f0f0f0;
}

.task-title {
  flex: 1;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.4;
  margin-right: 20rpx;
}

.task-status {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  font-weight: 500;
  white-space: nowrap;
}

.status-pending {
  background: #fff7e6;
  color: #fa8c16;
}

.status-todo {
  background: #e6f7ff;
  color: #1890ff;
}

.status-doing {
  background: #f6ffed;
  color: #52c41a;
}

.status-done {
  background: #f0f0f0;
  color: #666;
}

.detail-content {
  padding: 20rpx;
}

.info-section,
.location-section,
.images-section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding-bottom: 10rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.info-item {
  display: flex;
  margin-bottom: 16rpx;
  font-size: 26rpx;
}

.info-label {
  color: #999;
  min-width: 140rpx;
}

.info-value {
  color: #333;
  flex: 1;
}

.location-list {
  margin-top: 20rpx;
}

.location-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
}

.location-info {
  flex: 1;
}

.location-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.location-desc {
  font-size: 24rpx;
  color: #666;
}

.location-status {
  padding: 6rpx 12rpx;
  border-radius: 16rpx;
  font-size: 20rpx;
  font-weight: 500;
}

.location-pending {
  background: #fff7e6;
  color: #fa8c16;
}

.location-doing {
  background: #f6ffed;
  color: #52c41a;
}

.location-done {
  background: #f0f0f0;
  color: #666;
}

.images-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
  margin-top: 20rpx;
}

.detail-image {
  width: 100%;
  height: 200rpx;
  border-radius: 12rpx;
}

.detail-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #f0f0f0;
  display: flex;
  gap: 20rpx;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  border: none;
  border-radius: 40rpx;
  font-size: 28rpx;
  font-weight: bold;
}

.action-btn.primary {
  background: #1890ff;
  color: white;
}

.action-btn.success {
  background: #52c41a;
  color: white;
}
</style></div>

    <div class="code-block"><template>
  <userInfo/>
  <view class="container">
    <view class="menu-list">
      <view class="menu-item" v-for="(item, index) in menuList" :key="index" @click="handleMenuClick(item)">
        <view class="menu-item-left">
          <image class="menu-icon" :src="item.icon" mode="aspectFit"/>
          <text class="menu-text">{{ item.text }}</text>
        </view>
        <view class="menu-item-right">
          <text v-if="item.value" class="menu-value">{{ item.value }}</text>
          <image v-if="item.path" class="arrow-icon" src="/static/common/arrow-right.png" mode="aspectFit"/>
        </view>
      </view>
    </view>
    <CustomTabBar ref="customTabBar"/>
  </view>
</template>

<script setup>
import { onMounted, getCurrentInstance, ref } from 'vue'
import { onShow } from "@dcloudio/uni-app"
import userInfo from "@/components/userInfo/userInfo.vue"
import CustomTabBar from '@/components/CustomTabBar/CustomTabBar.vue'
import tabBarManager from '@/utils/tabBar'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()

const menuList = ref([
  {
    icon: '/static/mine/settings.png',
    text: '系统设置',
    path: '/pages/mine/settings'
  },
  {
    icon: '/static/mine/about.png',
    text: '关于我们',
    path: '/pages/mine/about'
  },
  {
    icon: '/static/mine/feedback.png',
    text: '意见反馈',
    path: '/pages/mine/feedback'
  },
  {
    icon: '/static/mine/privacy.png',
    text: '隐私政策',
    path: '/pages/contact/privacy'
  },
  {
    icon: '/static/mine/logout.png',
    text: '退出登录',
    action: 'logout'
  }
])

onMounted(() => {
  tabBarManager.init()
})

const updateTabBarIndex = () => {
  const tabBarComponent = getCurrentInstance()?.refs?.customTabBar
  if (tabBarComponent && tabBarComponent.setCurrentIndex) {
    tabBarComponent.setCurrentIndex(3)
  }
}

onShow(() => {
  updateTabBarIndex()
})

const handleMenuClick = (item) => {
  if (item.action === 'logout') {
    uni.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          userStore.logout()
        }
      }
    })
  } else if (item.path) {
    uni.navigateTo({
      url: item.path
    })
  }
}
</script>

<style scoped>
.container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 120rpx;
}

.menu-list {
  background: white;
  margin: 20rpx;
  border-radius: 16rpx;
  overflow: hidden;
}

.menu-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item-left {
  display: flex;
  align-items: center;
}

.menu-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 24rpx;
}

.menu-text {
  font-size: 28rpx;
  color: #333;
}

.menu-item-right {
  display: flex;
  align-items: center;
}

.menu-value {
  font-size: 26rpx;
  color: #999;
  margin-right: 16rpx;
}

.arrow-icon {
  width: 24rpx;
  height: 24rpx;
}
</style></div>

    <div class="code-block"><template>
  <view class="user-info">
    <view class="user-avatar">
      <image :src="userInfo?.avatar || '/static/default-avatar.png'" class="avatar-img"/>
    </view>
    <view class="user-details">
      <view class="user-name">{{ userInfo?.nickname || '未登录' }}</view>
      <view class="user-role">{{ userInfo?.roleName || '普通用户' }}</view>
      <view class="user-hospital" v-if="userInfo?.hospitalName">
        {{ userInfo.hospitalName }}
      </view>
    </view>
    <view class="user-actions">
      <view class="action-btn" @click="editProfile">
        <image src="/static/common/edit.png" class="action-icon"/>
      </view>
    </view>
  </view>
</template>

<script setup>
import { computed } from 'vue'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()

const userInfo = computed(() => userStore.getUserInfo)

const editProfile = () => {
  uni.navigateTo({
    url: '/pages/mine/profile'
  })
}
</script>

<style scoped>
.user-info {
  display: flex;
  align-items: center;
  padding: 40rpx 30rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.user-avatar {
  margin-right: 30rpx;
}

.avatar-img {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.user-role {
  font-size: 26rpx;
  opacity: 0.8;
  margin-bottom: 8rpx;
}

.user-hospital {
  font-size: 24rpx;
  opacity: 0.7;
}

.user-actions {
  display: flex;
  align-items: center;
}

.action-btn {
  width: 60rpx;
  height: 60rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 30rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-icon {
  width: 32rpx;
  height: 32rpx;
}
</style></div>

    <div class="code-block"><template>
  <view class="register-container">
    <view class="register-header">
      <view class="header-title">用户注册</view>
      <view class="header-subtitle">创建您的账户</view>
    </view>

    <form class="register-form">
      <view class="form-group">
        <CustomInput
          v-model="formData.username"
          placeholder="请输入用户名"
          prefixIcon="👤"
          :maxlength="20"
          clearable
        />
      </view>

      <view class="form-group">
        <CustomInput
          v-model="formData.password"
          type="password"
          placeholder="请输入密码"
          prefixIcon="🔒"
          :maxlength="20"
        />
      </view>

      <view class="form-group">
        <CustomInput
          v-model="formData.confirmPassword"
          type="password"
          placeholder="请确认密码"
          prefixIcon="🔒"
          :maxlength="20"
        />
      </view>

      <view class="form-group">
        <CustomInput
          v-model="formData.nickname"
          placeholder="请输入昵称"
          prefixIcon="✏️"
          :maxlength="10"
          clearable
        />
      </view>

      <view class="form-group">
        <CustomInput
          v-model="formData.phone"
          type="tel"
          placeholder="请输入手机号"
          prefixIcon="📱"
          :maxlength="11"
        />
      </view>

      <view class="form-group">
        <CustomInput
          v-model="formData.email"
          type="email"
          placeholder="请输入邮箱"
          prefixIcon="📧"
          clearable
        />
      </view>

      <view class="agreement-section">
        <label class="agreement-checkbox">
          <checkbox-group @change="handleAgreementChange">
            <checkbox :checked="agreed" value="true"/>
          </checkbox-group>
          <text class="agreement-text">
            我已阅读并同意
            <text class="agreement-link" @click="showUserAgreement">《用户协议》</text>
            和
            <text class="agreement-link" @click="showPrivacyPolicy">《隐私政策》</text>
          </text>
        </label>
      </view>

      <button class="register-btn" :disabled="!canRegister" @click="handleRegister">
        注册
      </button>

      <view class="login-link">
        <text>已有账号？</text>
        <text class="link-text" @click="goToLogin">立即登录</text>
      </view>
    </form>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue'
import CustomInput from '@/components/CustomInput/CustomInput.vue'
import * as AuthApi from '@/api/register/index'

const formData = ref({
  username: '',
  password: '',
  confirmPassword: '',
  nickname: '',
  phone: '',
  email: ''
})

const agreed = ref(false)

const canRegister = computed(() => {
  return formData.value.username &&
         formData.value.password &&
         formData.value.confirmPassword &&
         formData.value.nickname &&
         formData.value.phone &&
         agreed.value &&
         formData.value.password === formData.value.confirmPassword
})

const handleAgreementChange = (event) => {
  agreed.value = event.detail.value[0] === 'true'
}

const handleRegister = async () => {
  if (!canRegister.value) {
    uni.showToast({
      title: '请完善注册信息',
      icon: 'none'
    })
    return
  }

  if (formData.value.password !== formData.value.confirmPassword) {
    uni.showToast({
      title: '两次密码输入不一致',
      icon: 'none'
    })
    return
  }

  try {
    uni.showLoading({ title: '注册中...' })

    const result = await AuthApi.register({
      username: formData.value.username,
      password: formData.value.password,
      nickname: formData.value.nickname,
      phone: formData.value.phone,
      email: formData.value.email
    })

    if (result.code === 200) {
      uni.showToast({
        title: '注册成功',
        icon: 'success'
      })

      setTimeout(() => {
        uni.navigateBack()
      }, 1500)
    }
  } catch (error) {
    console.error('注册失败:', error)
  } finally {
    uni.hideLoading()
  }
}

const showUserAgreement = () => {
  uni.navigateTo({
    url: '/pages/contact/user-agreement'
  })
}

const showPrivacyPolicy = () => {
  uni.navigateTo({
    url: '/pages/contact/privacy'
  })
}

const goToLogin = () => {
  uni.navigateBack()
}
</script>

<style scoped>
.register-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 100rpx 40rpx 40rpx;
}

.register-header {
  text-align: center;
  margin-bottom: 80rpx;
  color: white;
}

.header-title {
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
}

.header-subtitle {
  font-size: 28rpx;
  opacity: 0.8;
}

.register-form {
  background: white;
  border-radius: 24rpx;
  padding: 60rpx 40rpx;
}

.form-group {
  margin-bottom: 40rpx;
}

.agreement-section {
  margin-bottom: 40rpx;
}

.agreement-checkbox {
  display: flex;
  align-items: flex-start;
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
}

.agreement-text {
  margin-left: 16rpx;
}

.agreement-link {
  color: #007aff;
  text-decoration: underline;
}

.register-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 40rpx;
}

.register-btn:disabled {
  background: #ccc;
}

.login-link {
  text-align: center;
  font-size: 26rpx;
  color: #666;
}

.link-text {
  color: #007aff;
  margin-left: 8rpx;
}
</style></div>
