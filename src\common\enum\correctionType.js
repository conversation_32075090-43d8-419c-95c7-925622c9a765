// 整改类型
export const CorrectionType = {
    NO_CORRECTION: 10,       // 无需整改
    IMMEDIATE_CORRECTION: 20, // 立即整改
    FUTURE_CORRECTION: 30,    // 后期整改

    // 获取整改类型描述
    getDescription(type) {
        switch (type) {
            case this.NO_CORRECTION:
                return "无需整改";
            case this.IMMEDIATE_CORRECTION:
                return "立即整改";
            case this.FUTURE_CORRECTION:
                return "后期整改";
            default:
                return "未知类型";
        }
    },

    // 获取所有整改类型的值和描述
    getAll() {
        return [
         //    {value: this.NO_CORRECTION, label: this.getDescription(this.NO_CORRECTION)},
            {value: this.IMMEDIATE_CORRECTION, label: this.getDescription(this.IMMEDIATE_CORRECTION)},
            {value: this.FUTURE_CORRECTION, label: this.getDescription(this.FUTURE_CORRECTION)},
        ];
    }
}
