<template>
  <view class="container">
    <!-- 主体区域 -->
    <view class="detail-content" v-if="taskInfo">
      <!-- 任务基本信息卡片 -->
      <view class="info-card">
        <view class="card-header">
          <text class="card-title">任务基本信息</text>
        </view>

        <view class="info-item">
          <text class="info-label">任务标题：</text>
          <text class="info-value">{{ taskInfo.taskTitle }}</text>
        </view>

        <view class="info-item">
          <text class="info-label">任务描述：</text>
          <text class="info-value">{{ currentPoints.pointsDesc }}</text>
        </view>
        
        <view class="info-item">
          <text class="info-label">创建时间：</text>
          <text class="info-value">{{ formatDate(taskInfo.createdAt) }}</text>
        </view>
        
        <view class="info-item">
          <text class="info-label">执行时间：</text>
          <text class="info-value">
            {{ formatDate(taskInfo.beginDate) }} 至 {{ formatDate(taskInfo.endDate) }}
          </text>
        </view>
        
        <view class="info-item">
          <text class="info-label">当前状态：</text>
          <text class="info-value status" :class="getStatusClass(taskInfo.status)">
            {{ TaskStatus.getDescription(taskInfo.status) }}
          </text>
        </view>
      </view>
      
      <!-- 巡查照片区域 -->
      <view class="info-card" v-if="patrolImage && patrolImage.length > 0">
        <view class="card-header">
          <text class="card-title">巡查照片</text>
        </view>
        
        <view class="image-grid">
          <view 
            class="image-item" 
            v-for="(image, index) in patrolImage" 
            :key="index"
            @click="previewImage(image.url, patrolImage)"
          >
            <image class="grid-image" :src="image.url" mode="aspectFill" />
          </view>
        </view>
      </view>
      
      <!-- 整改后照片区域 -->
      <view class="info-card" v-if="currentPoints && currentPoints.status === 20 && finishImage && finishImage.length > 0">
        <view class="card-header">
          <text class="card-title">整改后照片</text>
        </view>
        
        <view class="image-grid">
          <view 
            class="image-item" 
            v-for="(image, index) in finishImage" 
            :key="index"
            @click="previewImage(image.url, finishImage)"
          >
            <image class="grid-image" :src="image.url" mode="aspectFill" />
          </view>
        </view>
      </view>
      
      <!-- 操作按钮区域 -->
      <view class="action-area">
        <button class="secondary-btn" @click="goBack">返回</button>
      </view>
    </view>
    
    <!-- 加载状态 -->
    <view class="loading-container" v-else>
      <view class="loading">
        <text>加载中...</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance } from 'vue'
import * as Api from '@/api/user/index'
import { formatDate } from "@/utils/date"
import { TaskStatus } from "@/common/enum/taskStatus"

// 任务ID
const tasksId = ref(null)
// 任务详情
const taskInfo = ref(null)
// 当前质控点
const currentPoints = ref(null)
// 巡查图片
const patrolImage = ref([])
// 整改后图片
const finishImage = ref([])

// 获取路由参数并加载任务详情
onMounted(async () => {
  const instance = getCurrentInstance().proxy
  tasksId.value = instance.$page.options.tasksId
  
  if (tasksId.value) {
    await loadTaskDetail()
  } else {
    uni.showToast({
      title: '任务ID不存在',
      icon: 'none'
    })
    setTimeout(() => {
      goBack()
    }, 1500)
  }
})

// 加载任务详情
const loadTaskDetail = async () => {
  try {
    const res = await Api.getCorrectionDetail(tasksId.value)
    
    if (res.code === 200) {
      taskInfo.value = res.data.tasksInfo
      currentPoints.value = res.data.currentPoints
      
      // 处理巡查图片
      if (currentPoints.value && currentPoints.value.patrolImage) {
        patrolImage.value = currentPoints.value.patrolImage.map(item => ({url: item.imageUrl}))
      }
      
      // 处理整改后图片
      if (currentPoints.value && currentPoints.value.status === 20 && currentPoints.value.finishImage) {
        finishImage.value = currentPoints.value.finishImage.map(item => ({url: item.imageUrl}))
      }
    } else {
      uni.showToast({
        title: res.message || '获取任务详情失败',
        icon: 'none'
      })
    }
  } catch (error) {
    console.error('获取任务详情失败:', error)
    uni.showToast({
      title: '获取任务详情失败',
      icon: 'none'
    })
  }
}

// 图片预览
const previewImage = (current, imageList) => {
  const urls = imageList.map(img => img.url)
  uni.previewImage({
    current,
    urls
  })
}

// 返回上一页
const goBack = () => {
  uni.navigateBack()
}

// 获取任务状态样式类
const getStatusClass = (status) => {
  switch (status) {
    case TaskStatus.PENDING_ACCEPT:
      return 'status-pending'
    case TaskStatus.PENDING_EXECUTE:
      return 'status-waiting'
    case TaskStatus.IN_PROGRESS:
      return 'status-progress'
    case TaskStatus.COMPLETED:
      return 'status-completed'
    default:
      return ''
  }
}
</script>

<style scoped>
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 40rpx;
}

.detail-content {
  padding: 30rpx;
}

.info-card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.card-header {
  border-bottom: 1px solid #eee;
  padding-bottom: 20rpx;
  margin-bottom: 20rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.info-item {
  display: flex;
  margin-bottom: 20rpx;
  font-size: 28rpx;
  line-height: 40rpx;
}

.info-label {
  width: 180rpx;
  color: #666;
  flex-shrink: 0;
}

.info-value {
  color: #333;
  flex: 1;
}

.status {
  font-weight: bold;
}

.status-pending {
  color: #ff9800;
}

.status-waiting {
  color: #2196f3;
}

.status-progress {
  color: #4caf50;
}

.status-completed {
  color: #607d8b;
}

.image-grid {
  display: flex;
  flex-wrap: wrap;
  margin: 20rpx -10rpx 0;
}

.image-item {
  width: 33.33%;
  padding: 10rpx;
  box-sizing: border-box;
}

.grid-image {
  width: 100%;
  height: 200rpx;
  border-radius: 8rpx;
  background-color: #f5f5f5;
}

.action-area {
  display: flex;
  flex-direction: column;
  margin-top: 40rpx;
}

.primary-btn, .secondary-btn {
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  font-size: 30rpx;
  border-radius: 40rpx;
  margin-bottom: 20rpx;
}

.primary-btn {
  background-color: #4caf50;
  color: #fff;
}

.secondary-btn {
  background-color: #f5f5f5;
  color: #666;
  border: 1px solid #ddd;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300rpx;
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
}

.loading::before {
  content: "";
  width: 30rpx;
  height: 30rpx;
  margin-right: 10rpx;
  border: 2px solid #999;
  border-top-color: transparent;
  border-radius: 50%;
  animation: loading 0.8s linear infinite;
}

@keyframes loading {
  to {
    transform: rotate(360deg);
  }
}
</style> 