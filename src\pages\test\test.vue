<template>
	<view> 获取到的token： {{store.token}} </view>
	<view> 登陆状态： {{store.isLogined}} </view>
  <view>用户信息  : {{user}}</view>

  <button @click="getToken">打印token</button>

  <button @click="getUser">打印用户信息</button>

  <view style="width: 100%; height: 100rpx;"></view>

  <uploadImage class="upload-image" v-model:fileList="fileList" />
</template>

<script setup>
	import { useUserStore } from '@/stores/user'
  import uploadImage from '@/components/uploadImage/uploadImage.vue'
  import {ref} from "vue";
	const store = useUserStore()

  const getUser = () =>{

  // 默认获取用户信息（优先缓存）
    store.fetchUserInfo().then(userInfo => {
        console.log(userInfo)
    })

  // 强制刷新用户信息
  //     userStore.fetchUserInfo(true).then(userInfo => {
  //       console.log(userInfo)
  //     }

  }

	const getToken = () => {
		console.log(store.token)
	}

  // 上传图片
  const fileList = ref([{
    url: "http://127.0.0.1:8888/uploads/file/c9aba8265d54c0b931c65e652f3aaf3a_20241210120714.png"
  }])

  const fileList2 = ref([])
</script>

<style>
.upload-image {
}
</style>
