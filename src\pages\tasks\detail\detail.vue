<template>
  <div v-bind="$attrs">
    <!-- 用户信息 -->
    <userInfo/>

    <!-- 主体区域 -->
    <view class="container" v-if="taskInfo">
      <!-- 任务类型 -->
      <view class="tasks-type">{{ TaskType.getDescription(taskInfo.taskType) }}</view>
      <!-- 任务信息 -->
      <view class="tasks-info">
        <view class="tasks-info-top">
          <view class="task-title">
            <span>整改任务</span>
            <text class="task-date">{{ formatTimeAgo(taskInfo.createdAt) }}</text>
          </view>
          <view class="tasks-no">编号：{{ taskInfo.executePlanId }}-{{ taskInfo.id }}</view>
        </view>
        <view class="task-desc">{{ taskInfo.taskTitle }}</view>
      </view>

      <!-- 巡查地点列表 -->
      <view class="location-list">
        <location-list :locations="taskInfo.location" title="选择当前到达地点（可左右滑动），拍照反馈"
                       :currentIndex="locationIndex" @indexChange="locationIndexChange"/>
      </view>

      <view class="point-content">
        <view class="points-title">请点击以下要点，逐项检查核对</view>
        <!-- 质控点序号 -->
        <view class="tasks-card">
          <view class="points-index">
            <view v-for="(item, index) in pointsList" :key="index" class="point-wrapper">
              <view class="point-circle"
                    :class="{
                      'bg-notdone': item.status === 10,
                      'bg-done': item.status === 20,
                      'bg-error': item.status === 30
                    }"
                    @click="pointsCheckHandler(index)"
              >
                {{ index + 1 }}
              </view>
              <!-- 添加底部指示点 -->
              <view v-if="index === pointsIndex" class="point-indicator"></view>
            </view>
          </view>
        </view>
      </view>

      <view class="tasks-card">
        <!-- 质控点名称 -->
        <view class="points-name">
          <text class="points-index-tag" type="success">{{ pointsIndex + 1 }}</text>
          <view class="point-index-title">
            <text>{{ currentPoints.pointsInfo.description }}
              <!--            <up-tag size="mini" -->
              <!--                    :type="completedRate < currentPoints.pointsInfo.inspectRate ? 'warning':'success'"  -->
              <!--                    :class="{ 'rate-red':completedRate < currentPoints.pointsInfo.inspectRate}" -->
              <!--                    @click="showRateModal">-->
              <!--              {{ currentPoints.pointsInfo.inspectRate }}%-->
              <!--            </up-tag>-->
              <up-tag size="mini" type="primary" v-if="currentPoints.pointsInfo.feedbackWay === 20"  @click="showRateModal">
                {{ completedRate }}%
              </up-tag>
            </text>
          </view>
        </view>
        <up-form ref="formRef">
          <!-- 选择项 -->
          <view class="points-choices" v-if="currentPoints.pointsInfo.feedbackWay === 10">
              <up-radio-group v-model="formData.feedbackType " placement="row" shape="square" :disabled="disabled">
                <up-radio v-for="(item, index) in FeedbackType.getAll()" :key="index" :label="item.label"
                          :name="item.value"/>
              </up-radio-group>
          </view>
          <view v-else>
            <up-form-item :label="FeedbackType.getDescription(FeedbackType.FULLY_COMPLIANT) + ':'" label-width="100">
              <up-input type="number" placeholder="请填写数量" v-model.number="formData.completelyConformingCount"
                        style="background: #ffffff;" :disabled="disabled"/>
            </up-form-item>
            <up-form-item :label="FeedbackType.getDescription(FeedbackType.PARTIALLY_COMPLIANT) + ':'"
                          label-width="100">
              <up-input type="number" placeholder="请填写数量" v-model.number="formData.partiallyConformingCount"
                        style="background: #ffffff;" :disabled="disabled"/>
            </up-form-item>
            <up-form-item :label="FeedbackType.getDescription(FeedbackType.NON_COMPLIANT) + ':'" label-width="100">
              <up-input type="number" placeholder="请填写数量" v-model.number="formData.nonConformingCount"
                        style="background: #ffffff;" :disabled="disabled"/>
            </up-form-item>
          </view>

          <view v-if="formData.feedbackType !== FeedbackType.FULLY_COMPLIANT">
            <view class="problem_desc-wap">
              <up-input class="problem_desc-input" placeholder="请输入问题描述" border="surround"
                        v-model="formData.problemDesc" :disabled="disabled"/>
            </view>
            <!-- 巡查图片-->
            <upload-image class="upload-image" v-model:fileList="formData.patrolImage"
                          title="请拍照巡查照片（至少1张，最多9张）" :disabled="disabled"/>
            <!--   整改   -->
            <view style="margin:20rpx 0;">该地点具有不符合项，请选择是否需要整改</view>
            <up-radio-group v-model="formData.correctionType" placement="row" :disabled="disabled">
              <up-radio v-for="(item, index) in CorrectionType.getAll()"
                        :key="index" :label="item.label" :name="item.value"/>
            </up-radio-group>
            <!-- 整改后图片 -->
            <upload-image v-if="formData.correctionType === CorrectionType.IMMEDIATE_CORRECTION" :disabled="disabled"
                          class="upload-image" v-model:fileList="formData.finishImage"
                          title="请拍摄或上传整改后的照片（至少1张，最多9张）"/>
          </view>
          <!--   提交按钮    -->
          <view class="points-btn-wap">
            <!--  禁止提交  -->
            <up-button class="points-btn" type="primary" disabled text="已保存"
                       v-if="currentPoints.status === 20 ||  currentLocationStatus === 20"/>
            <!--  保存提交  -->
            <up-button class="points-btn" type="primary" text="保存" @click="onsubmitHandler" v-else/>
          </view>
        </up-form>
      </view>
      <view class="btn-group">
        <up-button class="btn-finish" type="primary" text="该地点已完成巡查，上传巡查信息" @click="doLocationFinish"
                   :disabled="currentLocationStatus === 20"/>
        <up-button class="btn-publish" type="warning" text="不是我的巡查点，我要上报问题" @click="publishHandler"/>
      </view>
    </view>
  </div>
</template>

<script setup>
import {ref, watch, computed} from 'vue'
import {onLoad} from "@dcloudio/uni-app"
import * as TasksApi from '@/api/tasks'
import LocationList from '@/components/locationList/LocationList.vue'
import userInfo from "@/components/userInfo/userInfo.vue"
import uploadImage from '@/components/uploadImage/uploadImage.vue'
import {TaskType} from "@/common/enum/tasksType"
import {FeedbackType} from "@/common/enum/feedbackType"
import {CorrectionType} from "@/common/enum/correctionType"
import {formatTimeAgo} from "@/utils/date"
import {showToast} from "@dcloudio/uni-h5";

// 任务详情
const taskInfo = ref(null)
// 巡查点索引
const locationIndex = ref(0)
// 巡查点列表
const locationList = ref([])
// 质控点索引
const pointsIndex = ref(0)
// 质控点列表
const pointsList = ref([])
// 选中的之间点
const currentPoints = ref(null)
// 质控点完成数（同一计划周期内）
// const completedCount = ref(0)
// 质控点总数（同一计划周期内）
// const totalCount = ref(0)

// 已经巡查过的人数
const completePeopleCount = ref(0)

// 巡查率
const completedRate = computed(() => {
  if (taskInfo.value.peopleCount <= 0) {
    return 0
  }
  return (completePeopleCount.value / taskInfo.value.peopleCount * 100).toFixed(0)
})

// 是否可选择
const disabled = computed(() => {
  return currentPoints.value.status === 20 || locationList.value[locationIndex.value].status === 20
})

// 当前巡查点状态
const currentLocationStatus = computed(() => {
  return locationList.value[locationIndex.value].status
})

onLoad((options) => {
  console.log("onLoad 请求参数:", options)
  const tasksId = options.tasksId
  // 获取任务详情
  TasksApi.detail(tasksId).then((res) => {
    taskInfo.value = res.data.tasksInfo
    locationList.value = res.data.tasksInfo.location
    locationIndex.value = res.data.locationIndex
    pointsIndex.value = res.data.pointsIndex
    pointsList.value = res.data.pointsList
    currentPoints.value = res.data.currentPoints
    completePeopleCount.value = res.data.completePeopleCount
    // completedCount.value = res.data.completedCount
    // totalCount.value = res.data.totalCount
    // 重置formData数据
    resetFormData()
  })
})

// 表单数据
const formData = ref({
  feedbackType: null,    // 符合类型
  completelyConformingCount: 0, // 完全符合数量
  partiallyConformingCount: 0, //  部分符合数量
  nonConformingCount: 0, // 不符合数量
  problemDesc: "",  // 问题描述
  correctionType: CorrectionType.NO_CORRECTION, // 整改类型
  patrolImage: [],  //巡查图片
  finishImage: [], // 整改后照片
})

// 初始化数据
const resetFormData = () => {
  if (currentPoints.value.status !== 10) {
    formData.value = {
      feedbackType: currentPoints.value.feedbackType,    // 符合类型
      completelyConformingCount: currentPoints.value.completelyConformingCount || 0, // 完全符合数量
      partiallyConformingCount: currentPoints.value.partiallyConformingCount || 0, // 部分符合数量
      nonConformingCount: currentPoints.value.nonConformingCount || 0, // 不符合数量
      problemDesc: currentPoints.value.problemDesc,  // 问题描述
      correctionType: currentPoints.value.correctionType, // 整改类型
      patrolImage: currentPoints.value.patrolImage.map(item => ({url: item.imageUrl})),  // 巡查图片
      finishImage: currentPoints.value.finishImage.map(item => ({url: item.imageUrl})), // 整改后照片
    }
  } else {
    formData.value = {
      feedbackType: FeedbackType.FULLY_COMPLIANT,    // 符合类型
      completelyConformingCount: 0, // 完全符合数量
      partiallyConformingCount: 0, // 部分符合数量
      nonConformingCount: 0, // 不符合数量
      problemDesc: "",  // 问题描述
      correctionType: CorrectionType.NO_CORRECTION, // 整改类型
      patrolImage: [],  // 巡查图片
      finishImage: [], // 整改后照片
    }
  }
}

// 监听
watch(
    () => [formData.value.completelyConformingCount, formData.value.partiallyConformingCount, formData.value.nonConformingCount],
    (newValues, oldValues) => {
      // console.log("newValues>>>>",newValues)
      // console.log("oldValues>>>>",oldValues)
      // 数据变化计算判断是否为符合类型
      if (currentPoints.value.pointsInfo.feedbackWay === 10) return
      // 赋值 解决null不能比较的问题
      let completelyConformingCount = formData.value.completelyConformingCount ?? 0
      let partiallyConformingCount = formData.value.partiallyConformingCount ?? 0
      let nonConformingCount = formData.value.nonConformingCount ?? 0
      // 完全符合
      if (partiallyConformingCount === 0 && nonConformingCount === 0) {
        formData.value.feedbackType = FeedbackType.FULLY_COMPLIANT
        return
      }
      // 部分符合
      if (partiallyConformingCount !== 0 || nonConformingCount !== 0) {
        formData.value.feedbackType = FeedbackType.PARTIALLY_COMPLIANT
        return
      }
      // 完全不符合
      if (completelyConformingCount === 0 && partiallyConformingCount === 0) {
        formData.value.feedbackType = FeedbackType.NON_COMPLIANT
      }

    }, {deep: true}  // 设置 `deep: true` 确保监视嵌套对象的变化
)

// 切换巡查点
const locationIndexChange = (index) => {
  console.log("巡查点切换索引：：", index)
  console.log("巡查点名称：", taskInfo.value.location[index].locationName)
  // 获取列表
  let id = locationList.value[index].id
  TasksApi.getPointsListByLocationID(id).then((res) => {
    // console.log("巡查点下质控点列表：", res)
    locationIndex.value = index // 修改索引
    pointsIndex.value = 0     // 重置索引
    pointsList.value = res.data.pointsList// 质控点列表
    currentPoints.value = res.data.currentPoints  // 首个质控点详情
    completePeopleCount.value = res.data.completePeopleCount
    // completedCount.value = res.data.completedCount
    // totalCount.value = res.data.totalCount
    // 重置formData
    resetFormData()
  })
}

// 切换质控点
const pointsCheckHandler = (index) => {
  console.log("质控点切换后索引：：", index)
  if (index === pointsIndex.value) return
  // 获取质控点信息
  let id = pointsList.value[index].id
  TasksApi.getPointsDetail(id).then((res) => {
    pointsIndex.value = index
    currentPoints.value = res.data.currentPoints
    completePeopleCount.value = res.data.completePeopleCount
    // completedCount.value = res.data.completedCount
    // totalCount.value = res.data.totalCount
    // 重置formData
    resetFormData()
  })
}

// 保存
const onsubmitHandler = () => {
  console.log("保存表单")
  console.log("整改前照片：", formData.value.patrolImage.value)
  console.log("整改后照片：", formData.value.finishImage.value)
  console.log("表单数据", formData.value)
  // 数据校验
  if (!checkoutCommit()) return
  // 请求参数
  let params = {
    id: currentPoints.value.id,
    feedbackType: formData.value.feedbackType,
    completelyConformingCount: formData.value.completelyConformingCount || 0,
    partiallyConformingCount: formData.value.partiallyConformingCount || 0,
    nonConformingCount: formData.value.nonConformingCount || 0,
    problemDesc: formData.value.problemDesc,
    correctionType: formData.value.correctionType,
    patrolImage: formData.value.patrolImage.map(item => item.url),
    finishImage: formData.value.finishImage.map(item => item.url),
  }
  console.log("请求参数>>", params)
  // 请求
  TasksApi.submit(params).then(res => {
    // 提交成功 - 非后期整改，设置当前质控点提报完成
    if (formData.value.correctionType !== CorrectionType.FUTURE_CORRECTION) {
      currentPoints.value.status = 20
      pointsList.value[pointsIndex.value].status = 20
      // locationList.value[locationIndex.value].laterCorrectCount = locationList.value[locationIndex.value].laterCorrectCount - 1
      let total = params.completelyConformingCount + params.partiallyConformingCount + params.nonConformingCount
      completePeopleCount.value = completePeopleCount.value + total
    } else {
      currentPoints.value.status = 30
      pointsList.value[pointsIndex.value].status = 30
      // locationList.value[locationIndex.value].laterCorrectCount = locationList.value[locationIndex.value].laterCorrectCount + 1
    }
    // 更新巡查点下质控点状态
    locationList.value[locationIndex.value].pointsList.forEach((item, idx) => {
      if (currentPoints.value.id === item.id) {
        locationList.value[locationIndex.value].pointsList[idx].status = currentPoints.value.status
      }
    })
    uni.showToast({
      icon: "none",
      mask: true,
      title: res.message,
    })
  })
}

// 验证参数
const checkoutCommit = () => {
  // 填数得不能都为0
  // if (currentPoints.value.pointsInfo.feedbackWay === 20) {
  //   if (formData.value.completelyConformingCount === 0 && formData.value.partiallyConformingCount === 0
  //       && currentPoints.value.nonConformingCount === 0) {
  //     uni.showToast({
  //       title: '检查填报数量不能都为0',
  //       icon: 'none'
  //     })
  //     return false
  //   }
  // }
  // 非完全符合
  if (formData.value.feedbackType !== FeedbackType.FULLY_COMPLIANT) {
    // 问题描述
    if (formData.value.problemDesc === "") {
      uni.showToast({
        title: '必须填写问题描述',
        icon: 'none'
      })
      return false
    }
    // 上传问题图片
    if (formData.value.patrolImage.length === 0) {
      uni.showToast({
        title: '至少上传一张巡查照片',
        icon: 'none'
      })
      return false
    }
  }
  // 立即整改
  if (formData.value.correctionType === CorrectionType.IMMEDIATE_CORRECTION && formData.value.finishImage.length === 0) {
    uni.showToast({
      title: '至少上传一张整改后照片',
      icon: 'none'
    })
    return false
  }
  return true
}


// 完成巡查点
const doLocationFinish = async () => {
  // 获取当前巡查点的 id
  const currentLocation = locationList.value[locationIndex.value]

  // 添加确认弹窗
  uni.showModal({
    title: '确认完成',
    content: '确定已完成该巡查点的所有检查项目吗？',
    confirmText: '确定',
    cancelText: '取消',
    success: async (res) => {
      if (res.confirm) {
        try {
          const result = await TasksApi.locationFinish(currentLocation.id)
          if (result.code === 200) {
            uni.showToast({
              title: '巡查点已完成',
              icon: 'success'
            })
            // 更新当前巡查点状态
            currentLocation.status = 20
          } else {
            uni.showToast({
              title: result.message || '操作失败',
              icon: 'none'
            })
          }
        } catch (error) {
          console.error('完成巡查点失败:', error)
          uni.showToast({
            title: '操作失败',
            icon: 'none'
          })
        }
      }
    }
  })
}

const showRateModal = () => {
  uni.showModal({
    title: '质控点完成情况',
    content: `已巡查人数：${completePeopleCount.value}个\n总人数：${taskInfo.value.peopleCount }个`,
    showCancel: false,
    confirmText: '知道了'
  })
}

// 跳转到上报页面
const publishHandler = () => {
  uni.showModal({
    title: '确认跳转',
    content: '确定跳转到上报页面吗？',
    confirmText: '确定',
    cancelText: '取消',
    success: async (res) => {
      if (res.confirm) {
        uni.switchTab({
          url: '/pages/tasks/publish/index'
        })
      }
    }
  })
}
</script>

<style scoped>
@import './static/detail.css';
</style>
