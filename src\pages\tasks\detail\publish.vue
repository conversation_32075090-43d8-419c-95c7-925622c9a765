<template>
  <div v-bind="$attrs">
    <!-- 用户信息 -->
    <userInfo />

    <!-- 主体区域 -->
    <view class="task-container" v-if="taskInfo">
      <!-- 任务类型 -->
      <view class="tasks-type">整改任务</view>
      <!-- 任务信息 -->
      <view class="tasks-info">
        <view class="tasks-info-top">
          <view class="task-title">
            <span>整改任务</span>
            <text class="task-date">{{ formatTimeAgo(taskInfo.createdAt) }}</text>
          </view>
        </view>
        <view>任务标题：{{ taskInfo.taskTitle }}</view>
        <view>巡查地点：{{ locationName }}</view>
        <view>执行时间：{{ formatDate(taskInfo.beginDate) }} 至 {{ formatDate(taskInfo.endDate) }}</view>

        <!-- 巡查照片移到这里 -->
        <view class="patrol-images-section">
          <ImageUploader
            v-model="patrolImage"
            title="巡查照片："
            :disabled="true"
            :show-tip="false"
            :max-count="patrolImage.length"
          />
        </view>
      </view>

      <view class="tasks-card" style="margin-top: 40rpx;">
        <!-- 质控点名称 -->
        <view class="points-name">
          <text class="points-index-tag" type="success">1</text>
          <text class="point-index-title">
            {{ currentPoints.pointsDesc }}
          </text>
        </view>
        <CustomForm ref="formRef" :model="formData">
            <!-- 整改后图片 -->
            <ImageUploader
              v-model="formData.finishImage"
              title="请拍摄或上传整改后的照片(至少1张)"
              :max-count="9"
              :show-tip="false"
              :disabled="currentPoints.status===20"
              @change="onFinishImageChange"
            />
            <CustomFormItem label="无法整改原因：" prop="reason" labelPosition="top">
              <CustomInput v-model="formData.reason" placeholder="请输入无法整改原因" :disabled="currentPoints.status===20"/>
            </CustomFormItem>
          </CustomForm>
      </view>
      <view class="btn-group">
        <CustomButton
          class="btn-finish"
          type="primary"
          text="该任务已完成，上传提交"
          @click="doLocationFinish"
          :disabled="currentPoints.status===20"
        />
      </view>
    </view>
  </div>
</template>

<script setup>
import {ref, onMounted, getCurrentInstance, computed} from 'vue'
import * as TasksApi from '@/api/tasks'
import userInfo from "@/components/userInfo/userInfo.vue"
import ImageUploader from '@/components/ImageUploader/ImageUploader.vue'
import CustomButton from '@/components/CustomButton/CustomButton.vue'
import CustomInput from '@/components/CustomInput/CustomInput.vue'
import CustomForm from '@/components/CustomForm/CustomForm.vue'
import CustomFormItem from '@/components/CustomFormItem/CustomFormItem.vue'
import {formatDate, formatTimeAgo} from "@/utils/date";

// 任务详情
const taskInfo = ref(null)
// 选中的之间点
const currentPoints = ref(null)
const patrolImage = ref([]) //巡查图片
// 表单数据
const formData = ref({
  finishImage: [], // 整改后照片
  reason:"" // 整改完成不了的原因
})

const locationName = computed(() => {
  return taskInfo.value.location.length > 0 ? taskInfo.value.location[0].locationName : ""
})

// 图片变化处理函数
const onFinishImageChange = (imageList) => {
  console.log('整改后图片变化:', imageList)
  formData.value.finishImage = imageList
}

// 挂载时获取数据
onMounted(async () => {
  const instance = getCurrentInstance().proxy
  const tasksId = instance.$page.options.tasksId
  // 获取任务详情
  const res = await TasksApi.publishDetail(tasksId)
  // console.log(res)
  if (res.code === 200) {
    taskInfo.value = res.data.tasksInfo
    currentPoints.value = res.data.currentPoints
    // 巡查图片 - 转换为新组件格式
    patrolImage.value = currentPoints.value.patrolImage.map(item => ({
      url: item.imageUrl,
      status: 'success'
    }))

    if (currentPoints.value.status === 20) {
      // 整改后图片 - 转换为新组件格式
      formData.value.finishImage = currentPoints.value.finishImage.map(item => ({
        url: item.imageUrl,
        status: 'success'
      }))
      formData.value.reason = currentPoints.value.reason
    }
  }
})

// 保存
const doLocationFinish = () => {
  console.log("保存表单")
  console.log("整改后照片：", formData.value.finishImage)
  console.log("表单数据", formData.value)

  // 至少上传一张照片或填写原因
  if (formData.value.finishImage.length === 0 && formData.value.reason.trim() === "") {
    uni.showToast({
      title: '至少上传一张整改后的照片或填写无法完成原因',
      icon: 'none'
    })
    return
  }

  // 检查图片上传状态
  if (formData.value.finishImage.length > 0) {
    const uploadingImages = formData.value.finishImage.filter(img => img.status === 'uploading')
    if (uploadingImages.length > 0) {
      uni.showToast({
        title: '请等待图片上传完成',
        icon: 'none'
      })
      return
    }

    const errorImages = formData.value.finishImage.filter(img => img.status === 'error')
    if (errorImages.length > 0) {
      uni.showToast({
        title: '存在上传失败的图片，请重新上传',
        icon: 'none'
      })
      return
    }
  }

  // 提取成功上传的图片URL
  const successImages = formData.value.finishImage
    .filter(item => item.status === 'success' && item.url)
    .map(item => item.url)

  let params = {
    taskPointsId: currentPoints.value.id,
    finishImage: successImages,
    reason: formData.value.reason
  }
  console.log("请求参数>>", params)
  // 请求
  TasksApi.submitPublish(params).then(res => {
    console.log(res)
    uni.showToast({
      icon: "none",
      mask: true,
      title: res.message,
    })
    // 返回
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  })
}

</script>

<style scoped>
/* 简约风格样式 */
page {
  background: #efefef;
  min-height: 100vh;
}

.task-container {
  width: 100%;
  max-width: 750rpx;
  margin: 20rpx auto;
  padding: 0;
  background: #efefef;
  border-radius: 0;
  box-sizing: border-box;
}

/* 任务类型标签 */
.tasks-type {
  position: absolute;
  top: 80rpx;
  right: 30rpx;
  font-size: 24rpx;
  color: #666;
  font-weight: 500;
  padding: 8rpx 16rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20rpx;
  border: 1rpx solid #e0e0e0;
  z-index: 100;
}

/* 任务信息卡片 */
.tasks-info {
  background: white;
  margin: 20rpx;
  padding: 30rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  line-height: 1.5;
}

.tasks-info-top {
  margin-bottom: 20rpx;
  padding-bottom: 15rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.task-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100% !important;
  height: auto !important;
  line-height: 1.4;
}

.task-title span {
  font-size: 30rpx;
  font-weight: 500;
  color: #000;
}

.task-date {
  font-size: 24rpx !important;
  color: #666 !important;
  padding-left: 0 !important;
}

.tasks-info > view {
  margin: 15rpx 0 !important;
  color: #000;
  font-size: 26rpx;
}

.tasks-info > view::before {
  display: none;
}

/* 质控点卡片 */
.tasks-card {
  background: white;
  margin: 20rpx;
  padding: 30rpx;
  border-radius: 12rpx;
  position: relative;
}

.tasks-card::before {
  display: none;
}

/* 质控点名称 */
.points-name {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
  padding-bottom: 15rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.points-index-tag {
  width: 40rpx !important;
  height: 40rpx !important;
  line-height: 40rpx !important;
  font-size: 22rpx !important;
  background: #52c41a !important;
  border-radius: 50% !important;
  text-align: center;
  color: white !important;
  margin-right: 15rpx !important;
  margin: 0 15rpx 0 0 !important;
  float: none !important;
  flex-shrink: 0;
}

.point-index-title {
  font-size: 28rpx !important;
  font-weight: 500;
  color: #000;
  line-height: 1.4 !important;
  flex: 1;
}

/* 自定义表单样式 */
:deep(.custom-form) {
  padding: 0;
}

:deep(.custom-form-item) {
  margin-bottom: 30rpx;
}

:deep(.form-item-label) {
  margin-bottom: 16rpx;
}

:deep(.label-text) {
  font-size: 28rpx;
  color: #000;
  font-weight: 500;
}

:deep(.custom-input) {
  height: 80rpx;
  font-size: 30rpx;
}

/* 自定义按钮样式 */
:deep(.btn-finish) {
  width: 100%;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 30rpx;
  font-weight: 500;
}

/* 按钮组 */
.btn-group {
  padding: 30rpx 20rpx 60rpx;
}



/* 图片上传组件样式优化 */
:deep(.image-uploader) {
  margin: 25rpx 0;
}

:deep(.uploader-title) {
  font-size: 28rpx !important;
  color: #000 !important;
  font-weight: 500 !important;
  margin-bottom: 20rpx !important;
}

:deep(.image-list) {
  gap: 12rpx !important;
}

:deep(.image-item) {
  border-radius: 8rpx !important;
  overflow: hidden;
  border: 1rpx solid #e0e0e0;
  transition: all 0.3s ease;
}

:deep(.image-item:hover) {
  border-color: #bbb;
}

:deep(.add-btn) {
  border: 2rpx dashed #ccc !important;
  border-radius: 8rpx !important;
  background: #fafafa !important;
  transition: all 0.3s ease !important;
}

:deep(.add-btn:hover) {
  border-color: #999 !important;
  background: #f0f0f0 !important;
}

:deep(.add-icon) {
  color: #666 !important;
  font-size: 48rpx !important;
}

:deep(.add-text) {
  color: #666 !important;
  font-size: 24rpx !important;
}

:deep(.tip-text) {
  font-size: 24rpx !important;
  color: #666 !important;
  text-align: left;
  margin-top: 12rpx !important;
}

/* 响应式优化 */
@media (max-width: 750rpx) {
  .tasks-info,
  .tasks-card {
    margin: 20rpx;
    padding: 30rpx;
  }

  .btn-group {
    padding: 30rpx 20rpx 50rpx;
  }
}

/* 动画效果 */
.tasks-info,
.tasks-card {
  animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 加载状态 */
.btn-finish[loading] {
  position: relative;
  color: transparent !important;
}

.btn-finish[loading]::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 32rpx;
  height: 32rpx;
  margin: -16rpx 0 0 -16rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-top-color: white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
</style>
