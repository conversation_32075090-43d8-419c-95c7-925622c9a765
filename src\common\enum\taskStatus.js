// taskStatus.js 任务状态
export const TaskStatus = {
    PENDING_ASSIGN : 10,// 待指派
    PENDING_ACCEPT: 20, // 待接受
    PENDING_EXECUTE: 30, // 待执行
    IN_PROGRESS: 40,     // 执行中
    COMPLETED: 50,       // 已完成

    // 获取状态描述
    getDescription(status) {
        switch (status) {
            case this.PENDING_ASSIGN:
                return "待指派"
            case this.PENDING_ACCEPT:
                return "待接受"
            case this.PENDING_EXECUTE:
                return "待执行"
            case this.IN_PROGRESS:
                return "执行中"
            case this.COMPLETED:
                return "已完成"
            default:
                return "-"
        }
    }
}
