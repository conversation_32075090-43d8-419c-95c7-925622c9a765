import {defineStore} from 'pinia'
import * as Login<PERSON>pi from '@/api/login'
import * as UserApi from '@/api/user'
import storage from '@/utils/storage'
import {ref, computed} from 'vue'

const ACCESS_TOKEN = 'AccessToken'
const USER_ID = 'userId'

// 用户
export const useUserStore = defineStore('user', () => {
    const token = ref(storage.get(ACCESS_TOKEN) || '')
    const userId = ref(storage.get(USER_ID) || 0)
    const hospitalId = ref('') // hospitalId 仅在内存中管理
    const userInfo = ref(null)

    // 修改登录状态判断
    const isLogin = computed(() => {
        return !!token.value && userId.value !== 0
    })

    // 登录方法
    const login = async (username, password) => {
        try {
            const res = await LoginApi.login(username, password)
            if (res.code === 200) {
                // 设置过期时间 30 天
                const expiryTime = 30 * 86400
                token.value = res.data.token
                userId.value = res.data.uid
                hospitalId.value = res.data.hospitalId || ''
                // 保存到本地存储
                storage.set(USER_ID, res.data.uid, expiryTime)
                storage.set(ACCESS_TOKEN, res.data.token, expiryTime)
                return res.data
            }
        } catch (error) {
            throw error
        }
    }

    // 获取用户信息
    const fetchUserInfo = async (force = false) => {
        try {
            if (!force && userInfo.value) {
                return userInfo.value
            }
            const result = await UserApi.getUserInfo()
            userInfo.value = result.data
            hospitalId.value = result.data.hospitalId || '' // 更新 hospitalId
            return userInfo.value
        } catch (error) {
            console.error("获取用户信息失败", error)
            throw error
        }
    }

    // 登出方法
    const logout = () => {
        if (userId.value > 0) {
            storage.remove(USER_ID)
            storage.remove(ACCESS_TOKEN)
            token.value = ''
            userId.value = 0
            userInfo.value = null
        }
    }

    // 删除账号
    const deleteAccount = async () => {
        try {
            const res = await UserApi.deleteAccount()
            // 清空缓存
            storage.remove(USER_ID)
            storage.remove(ACCESS_TOKEN)
            token.value = ''
            userId.value = 0
            userInfo.value = null
            return res
        } catch (error) {
            console.error("删除用户失败", error)
            throw error
        }
    }


    return {
        token,
        userId,
        hospitalId,
        userInfo,
        isLogin,
        login,
        fetchUserInfo,
        logout,
        deleteAccount
    }
})
