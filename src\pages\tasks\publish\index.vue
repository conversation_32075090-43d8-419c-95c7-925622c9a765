<template>
  <!-- 用户信息 -->
  <userInfo/>

  <view class="container">
    <!-- 发布任务表单 -->
    <CustomForm class="form" labelPosition="top" labelWidth="300rpx" :model="formData" :rules="rules" ref="uForm">
      <!-- 院区选择 -->
      <CustomFormItem label="所属院区" prop="hospitalName" required>
        <view class="form-picker">
          <picker mode="selector" :range="hospitalColumns" range-key="label" :value="hospitalIndex" @change="onHospitalChange">
            <CustomInput v-model="formData.hospitalName" placeholder="请选择院区" :disabled="true"/>
          </picker>
        </view>
      </CustomFormItem>
      <!-- 任务标题 -->
      <CustomFormItem class="form-item" label="任务标题:" prop="taskTitle" ref="taskTitle" required>
        <CustomInput v-model="formData.taskTitle" placeholder="请输入任务标题"/>
      </CustomFormItem>
      <!-- 任务执行人 -->
      <CustomFormItem label="任务执行人" prop="executorId">
        <view class="form-picker">
          <picker mode="selector" :range="executors" rangeKey="nickName" @change="onExecutorChange">
            <CustomInput v-model="formData.executorName" placeholder="请选择任务执行人" :disabled="true"/>
          </picker>
        </view>
      </CustomFormItem>
      <!-- 巡查地点 -->
      <CustomFormItem label="巡查地点" prop="locationName" required>
        <CustomInput v-model="formData.locationName" placeholder="请选择或输入地址"/>
      </CustomFormItem>

      <!-- 任务描述 -->
      <CustomFormItem label="任务描述" prop="problemDesc" required>
        <CustomTextarea v-model="formData.problemDesc" placeholder="请输入任务描述"/>
      </CustomFormItem>
      <!-- 截至时间 -->
      <CustomFormItem label="截止日期" prop="endDate"  required>
        <view class="form-picker">
          <picker mode="date" :start="formatDate(new Date())" @change="endDateChange">
            <CustomInput v-model="formData.endDate" placeholder="请选择最迟完成时间" :disabled="true"/>
          </picker>
        </view>
      </CustomFormItem>
      <!-- 上传图片 -->
      <view>
        <ImageUploader
          v-model="formData.patrolImage"
          title="请拍摄相关地址巡查照片"
          :max-count="9"
          :required="true"
          @change="onImageChange"
        />
      </view>
      <CustomButton class="submit-button" type="primary" text="提交任务" @click="submitTask"/>
    </CustomForm>

    <!-- 自定义 TabBar -->
    <CustomTabBar ref="customTabBar" class="page-tabbar"/>
  </view>
</template>

<script setup>
import {ref, onMounted, nextTick, getCurrentInstance, watch} from "vue"
import * as UserApi from "@/api/user/index"
import * as PublishApi from '@/api/tasks/publish'
import userInfo from "@/components/userInfo/userInfo.vue"
import ImageUploader from '@/components/ImageUploader/ImageUploader.vue'
import CustomTabBar from '@/components/CustomTabBar/CustomTabBar.vue'
import CustomForm from '@/components/CustomForm/CustomForm.vue'
import CustomFormItem from '@/components/CustomFormItem/CustomFormItem.vue'
import CustomInput from '@/components/CustomInput/CustomInput.vue'
import CustomTextarea from '@/components/CustomTextarea/CustomTextarea.vue'
import CustomButton from '@/components/CustomButton/CustomButton.vue'

import tabBarManager from '@/utils/tabBar'
import {formatDate} from "@/utils/date";
import { useUserStore } from '@/stores/user'
import { onShow } from "@dcloudio/uni-app"

const userStore = useUserStore()


// 表单数据
const formData = ref({
  hospitalId: '', // 院区ID
  hospitalName: '', // 院区名称
  taskTitle: '',
  executorName:"",
  executorId: null,
  locationName: '',
  problemDesc: '',
  endDate: null,
  patrolImage: []
})

// 监控表单数据变化
watch(() => formData.value.taskTitle, (newVal, oldVal) => {
  console.log('taskTitle 变化:', oldVal, '->', newVal)
}, { immediate: true })

watch(() => formData.value.locationName, (newVal, oldVal) => {
  console.log('locationName 变化:', oldVal, '->', newVal)
}, { immediate: true })

watch(() => formData.value.problemDesc, (newVal, oldVal) => {
  console.log('problemDesc 变化:', oldVal, '->', newVal)
}, { immediate: true })

// 院区选择器数据
const hospitalColumns = ref([])
const hospitalIndex = ref(0)

// 页面显示时更新 TabBar 状态
const updateTabBarIndex = () => {
  // 通过 ref 获取 CustomTabBar 组件实例并更新索引
  const tabBarComponent = getCurrentInstance()?.refs?.customTabBar
  if (tabBarComponent && tabBarComponent.setCurrentIndex) {
    tabBarComponent.setCurrentIndex(2) // 发布页面对应索引 2
  }
}

// 获取用户信息和院区列表
onMounted(async () => {
  // 初始化 tabBarManager
  tabBarManager.init()

  try {
    const userInfo = await userStore.fetchUserInfo()

    // 转换院区数据为选择器格式
    if (userInfo.hospitals && userInfo.hospitals.length > 0) {
      hospitalColumns.value = userInfo.hospitals.map(hospital => ({
        label: hospital.areaName,
        value: hospital.id
      }))

      // 默认选中用户当前院区
      if (userInfo.hospitalId) {
        const index = userInfo.hospitals.findIndex(h => h.id === userInfo.hospitalId)
        if (index !== -1) {
          formData.value.hospitalId = userInfo.hospitals[index].id
          formData.value.hospitalName = userInfo.hospitals[index].areaName
          hospitalIndex.value = index
        }
      }

      // 获取执行人列表
      getExecutors(userInfo.hospitalId)
    }
  } catch (error) {
    console.error('获取用户信息失败', error)
  }
})

onShow(() => {
  // 更新 TabBar 状态
  updateTabBarIndex()
})


// 院区选择器改变事件
const onHospitalChange = async (e) => {
  console.log('院区选择改变事件:', e)
  const index = e.detail.value

  if (hospitalColumns.value[index]) {
    const selectedHospital = hospitalColumns.value[index]
    console.log('选择的院区:', selectedHospital)

    // 如果选择的是同一个院区，直接返回
    if (selectedHospital.value == formData.value.hospitalId) {
      console.log('选择的是同一个院区')
      return
    }

    // 更新院区信息
    formData.value.hospitalId = selectedHospital.value
    formData.value.hospitalName = selectedHospital.label
    hospitalIndex.value = index

    console.log('更新后的院区信息:', {
      hospitalId: formData.value.hospitalId,
      hospitalName: formData.value.hospitalName,
      hospitalIndex: hospitalIndex.value
    })

    // 重置执行人信息
    formData.value.executorId = null
    formData.value.executorName = ''

    // 等待下一个 tick，确保值已经更新
    await nextTick()
    // 手动触发表单验证
    uForm.value.validateField('hospitalName')
    // 获取新院区的执行人列表
    getExecutors(selectedHospital.value)
  }
}

// 执行人列表
const executors = ref([])

const getExecutors = (hospitalId) => {
  UserApi.getUserListByHospitalId(hospitalId).then((res) => {
    executors.value = res.data
  })
}

// 选择时间
const endDateChange = (e) =>{
  console.log(e.detail.value)
  formData.value.endDate = e.detail.value
}

const rules = ref({
  hospitalName: [{required: true, message: '请选择院区', trigger: ['blur', 'change']}],
  taskTitle: [{required: true, message: '任务标题不能为空', trigger: 'blur'}],
  locationName: [{required: true, message: '巡查地点不能为空', trigger: 'blur'}],
  problemDesc: [{required: true, message: '任务描述不能为空', trigger: 'blur'}],
  endDate: [{ required: true, message: "请选择截止日期", trigger: "blur" }],
})

// 选择执行人
const onExecutorChange = (e) => {
  let index = e.detail.value
  console.log("选择的索引是：", index)
  formData.value.executorName = executors.value[index].nickName
  formData.value.executorId = executors.value[index].id
}

// 图片变化处理
const onImageChange = (imageList) => {
  console.log('图片列表变化:', imageList)
  formData.value.patrolImage = imageList
}

const uForm = ref(null); // 引用表单实例

// 提交表单
const submitTask = async () => {
  const valid = await formValidate();
  if (!valid) return

   // 验证巡查照片
   if (formData.value.patrolImage.length === 0) {
    uni.showToast({
      title: '请上传巡查照片',
      icon: 'none',
      duration: 2500
    })
    return
  }

  // 检查是否有上传中的图片
  const uploadingImages = formData.value.patrolImage.filter(img => img.status === 'uploading')
  if (uploadingImages.length > 0) {
    uni.showToast({
      title: '请等待图片上传完成',
      icon: 'none',
      duration: 2500
    })
    return
  }

  // 检查是否有上传失败的图片
  const errorImages = formData.value.patrolImage.filter(img => img.status === 'error')
  if (errorImages.length > 0) {
    uni.showToast({
      title: '存在上传失败的图片，请重新上传',
      icon: 'none',
      duration: 3000
    })
    return
  }

  // 提交数据
  let data = {
    hospitalId: formData.value.hospitalId,  // 添加院区ID
    taskTitle: formData.value.taskTitle,
    executorId: formData.value.executorId,
    locationName: formData.value.locationName,
    problemDesc: formData.value.problemDesc,
    endDate: formData.value.endDate,
    patrolImage: formData.value.patrolImage
      .filter(item => item.status === 'success' && item.url)
      .map(item => item.url),
  }

  PublishApi.commit(data).then(async (res) => {
    console.log("提交结果：", res)
    uni.showModal({
        title: "提示",
        content: res.message,
        showCancel: false,
        confirmText: "确定",
        success: async (res) => {
          if (res.confirm) {
              // 数据初始化下 - 使用新对象触发响应式更新
              formData.value = {
                hospitalId: formData.value.hospitalId, // 保留院区信息
                hospitalName: formData.value.hospitalName, // 保留院区名称
                taskTitle: '',
                executorName: '',
                executorId: null,
                locationName: '',
                problemDesc: '',
                endDate: null,
                patrolImage: [] // 新的空数组
              }
          }
        }
    })

    // 提交成功后，刷新角标数据
    await tabBarManager.refreshBadgeData()

  })
}



// 验证数据
const formValidate = () => {
  return new Promise((resolve) => {
    // 使用validate方法进行验证
    uForm.value.validate().then(() => {
      resolve(true)
    }).catch(() => {
      resolve(false)
    })
  })
}
</script>

<style scoped>
/* 全局样式重置 */
* {
  box-sizing: border-box;
  max-width: 100%;
}

view, button {
  box-sizing: border-box;
  max-width: 100%;
}

input, textarea {
  box-sizing: border-box;
  max-width: 100%;
  pointer-events: auto !important;
}

/* 防止元素超出屏幕 */
.container, .form, :deep(.custom-form), :deep(.custom-form-item),
:deep(.custom-input-wrapper), :deep(.custom-textarea-wrapper) {
  max-width: 100%;
  overflow-x: hidden;
}

.container {
  width: 100vw;
  max-width: 100%;
  min-height: 100vh;
  padding: 20rpx 30rpx calc(100rpx + env(safe-area-inset-bottom)) 30rpx; /* 上右下左，为 TabBar 留出空间，适配苹果安全区域 */
  box-sizing: border-box;
  overflow-x: hidden;
}

.form {
  width: 100%;
  max-width: 690rpx;
  background: white;
  margin: 0 auto;
  padding: 50rpx 40rpx;
  border-radius: 16rpx;
  box-sizing: border-box;
}

.form-item {
  position: relative;
  width: 100%;
  box-sizing: border-box;
}

.form-picker {
  width: 100%;
  padding: 0;
  box-sizing: border-box;
}

/* 移除阻止输入的样式 */

/* 自定义表单样式 */
:deep(.custom-form) {
  width: 100%;
  padding: 0;
  box-sizing: border-box;
}

:deep(.custom-form-item) {
  width: 100%;
  margin-bottom: 40rpx;
  box-sizing: border-box;
}

:deep(.custom-form-item:last-child) {
  margin-bottom: 20rpx;
}

:deep(.form-item-label) {
  width: 100%;
  margin-bottom: 20rpx;
  box-sizing: border-box;
}

:deep(.label-text) {
  font-size: 30rpx;
  color: #000;
  font-weight: 500;
  line-height: 1.4;
}

:deep(.required-mark) {
  color: #ff4d4f;
  margin-left: 4rpx;
  font-size: 30rpx;
}

:deep(.form-item-content) {
  width: 100%;
  box-sizing: border-box;
}

/* 自定义输入框样式 */
:deep(.custom-input-wrapper) {
  width: 100%;
  border-radius: 8rpx;
  overflow: hidden;
  box-sizing: border-box;
  max-width: 100%;
}

:deep(.custom-input) {
  width: 100% !important;
  max-width: 100% !important;
  height: 88rpx !important;
  font-size: 30rpx !important;
  line-height: 1.4 !important;
  color: #000 !important;
  background: white !important;
  box-sizing: border-box !important;
  padding: 0 20rpx !important;
  border: none !important;
  outline: none !important;
  pointer-events: auto !important;
}

:deep(.input-surround) {
  border: 2rpx solid #d9d9d9 !important;
  border-radius: 8rpx !important;
}

:deep(.input-focus.input-surround) {
  border-color: #1890ff !important;
  box-shadow: 0 0 0 4rpx rgba(24, 144, 255, 0.1) !important;
}

:deep(.custom-textarea-wrapper) {
  width: 100%;
  border-radius: 8rpx;
  box-sizing: border-box;
}

:deep(.custom-textarea) {
  width: 100% !important;
  min-height: 160rpx !important;
  font-size: 30rpx !important;
  line-height: 1.6 !important;
  color: #000 !important;
  background: white !important;
  box-sizing: border-box !important;
  padding: 20rpx !important;
  pointer-events: auto !important;
}

:deep(.textarea-surround) {
  border: 2rpx solid #d9d9d9 !important;
  border-radius: 8rpx !important;
}

:deep(.textarea-focus.textarea-surround) {
  border-color: #1890ff !important;
  box-shadow: 0 0 0 4rpx rgba(24, 144, 255, 0.1) !important;
}

/* 禁用状态的输入框保持可点击外观 */
:deep(.input-disabled) {
  background: white !important;
  border-color: #d9d9d9 !important;
  cursor: pointer !important;
  pointer-events: auto !important;
}

:deep(.input-disabled .custom-input) {
  color: #000 !important;
  cursor: pointer !important;
  background: white !important;
  pointer-events: none !important;
}

/* 只读状态样式 */
:deep(.input-readonly) {
  background: white !important;
  cursor: pointer !important;
}

:deep(.input-readonly .custom-input) {
  cursor: pointer !important;
}

/* 自定义按钮样式 */
:deep(.submit-button) {
  width: 100% !important;
  height: 88rpx !important;
  border-radius: 8rpx !important;
  font-size: 32rpx !important;
  font-weight: 500 !important;
  margin-top: 60rpx !important;
  margin-bottom: 0 !important;
}

/* 选择器相关样式 */
.form-picker {
  position: relative;
  width: 100%;
}

.form-picker::after {
  content: '';
  position: absolute;
  right: 20rpx;
  top: 50%;
  width: 12rpx;
  height: 12rpx;
  border-right: 2rpx solid #999;
  border-bottom: 2rpx solid #999;
  transform: translateY(-50%) rotate(45deg);
  pointer-events: none;
  z-index: 10;
}

/* 选择器输入框样式 */
:deep(.form-picker .custom-input) {
  padding-right: 50rpx !important;
}

/* 选择器中的禁用输入框样式 */
:deep(.form-picker .input-disabled) {
  background: white !important;
  border-color: #d9d9d9 !important;
  cursor: pointer !important;
}

:deep(.form-picker .input-disabled .custom-input) {
  color: #000 !important;
  cursor: pointer !important;
  background: white !important;
  pointer-events: none !important;
}

/* 图片上传组件样式优化 */
:deep(.image-uploader) {
  margin: 40rpx 0 0 0;
}

:deep(.uploader-title) {
  font-size: 30rpx !important;
  color: #000 !important;
  font-weight: 500 !important;
  margin-bottom: 20rpx !important;
}

/* 响应式优化 */
@media (max-width: 750rpx) {
  .container {
    padding: 20rpx 24rpx calc(100rpx + env(safe-area-inset-bottom)) 24rpx;
  }

  .form {
    width: 100%;
    max-width: calc(100vw - 48rpx);
    margin: 0 auto;
    padding: 40rpx 32rpx;
  }

  :deep(.custom-form-item) {
    margin-bottom: 36rpx;
  }

  :deep(.label-text) {
    font-size: 32rpx;
  }

  :deep(.custom-input) {
    height: 96rpx !important;
    font-size: 32rpx !important;
    padding: 0 20rpx !important;
  }

  :deep(.form-picker .custom-input) {
    padding-right: 60rpx !important;
  }

  :deep(.custom-textarea) {
    min-height: 180rpx !important;
    font-size: 32rpx !important;
    padding: 24rpx !important;
  }

  :deep(.submit-button) {
    height: 96rpx !important;
    font-size: 34rpx !important;
    margin-top: 60rpx !important;
  }

  .form-picker::after {
    right: 20rpx;
  }
}

/* 确保 TabBar 在发布页面正确显示 */
.page-tabbar {
  position: fixed !important;
  bottom: 0 !important;
  left: 0 !important;
  right: 0 !important;
  z-index: 9999 !important;
}

/* 动画效果 */
.form {
  animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
