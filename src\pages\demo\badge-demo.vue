<template>
  <view class="container">
    <view class="header">
      <text class="title">角标功能演示</text>
    </view>

    <!-- 当前角标状态 -->
    <view class="badge-status">
      <text class="section-title">当前角标状态</text>
      <view class="status-grid">
        <view class="status-item">
          <text class="status-label">巡查</text>
          <text class="status-value">{{ taskBadge }}</text>
        </view>
        <view class="status-item">
          <text class="status-label">整改</text>
          <text class="status-value">{{ publishBadge }}</text>
        </view>
        <view class="status-item">
          <text class="status-label">发布</text>
          <text class="status-value">{{ createBadge }}</text>
        </view>
        <view class="status-item">
          <text class="status-label">我的</text>
          <text class="status-value">{{ mineBadge }}</text>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="operations">
      <text class="section-title">角标操作</text>
      
      <!-- 获取角标数据 -->
      <view class="operation-group">
        <text class="group-title">数据获取</text>
        <button class="operation-btn primary" @click="fetchBadgeData">获取角标数据</button>
        <button class="operation-btn" @click="refreshBadgeData">刷新角标数据</button>
      </view>

      <!-- 设置角标 -->
      <view class="operation-group">
        <text class="group-title">设置角标</text>
        <view class="btn-row">
          <button class="operation-btn small" @click="setBadge('task', 5)">巡查+5</button>
          <button class="operation-btn small" @click="setBadge('publish', 3)">整改+3</button>
          <button class="operation-btn small" @click="setBadge('create', 1)">发布+1</button>
          <button class="operation-btn small" @click="setBadge('mine', 2)">我的+2</button>
        </view>
      </view>

      <!-- 增减角标 -->
      <view class="operation-group">
        <text class="group-title">增减角标</text>
        <view class="btn-row">
          <button class="operation-btn small success" @click="incrementBadge('task')">巡查+1</button>
          <button class="operation-btn small success" @click="incrementBadge('publish')">整改+1</button>
          <button class="operation-btn small warning" @click="decrementBadge('task')">巡查-1</button>
          <button class="operation-btn small warning" @click="decrementBadge('publish')">整改-1</button>
        </view>
      </view>

      <!-- 显示小红点 -->
      <view class="operation-group">
        <text class="group-title">小红点</text>
        <view class="btn-row">
          <button class="operation-btn small info" @click="showDot('create')">发布显示红点</button>
          <button class="operation-btn small info" @click="showDot('mine')">我的显示红点</button>
        </view>
      </view>

      <!-- 清除角标 -->
      <view class="operation-group">
        <text class="group-title">清除角标</text>
        <view class="btn-row">
          <button class="operation-btn small danger" @click="clearBadge('task')">清除巡查</button>
          <button class="operation-btn small danger" @click="clearBadge('publish')">清除整改</button>
          <button class="operation-btn danger" @click="clearAllBadges">清除所有角标</button>
        </view>
      </view>
    </view>

    <!-- 自定义 TabBar -->
    <CustomTabBar @change="onTabBarChange" />
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useTabBarStore } from '@/stores/tabBar'
import tabBarManager from '@/utils/tabBar'
import CustomTabBar from '@/components/CustomTabBar/CustomTabBar.vue'

const store = useTabBarStore()

// 计算属性获取角标值
const taskBadge = computed(() => store.taskBadge)
const publishBadge = computed(() => store.publishBadge)
const createBadge = computed(() => store.createBadge)
const mineBadge = computed(() => store.mineBadge)

onMounted(() => {
  // 初始化 tabBarManager
  tabBarManager.init()
})

// 获取角标数据
const fetchBadgeData = async () => {
  try {
    uni.showLoading({ title: '获取中...' })
    await tabBarManager.fetchBadgeData()
    uni.hideLoading()
    uni.showToast({
      title: '获取成功',
      icon: 'success'
    })
  } catch (error) {
    uni.hideLoading()
    uni.showToast({
      title: '获取失败',
      icon: 'error'
    })
  }
}

// 刷新角标数据
const refreshBadgeData = async () => {
  try {
    uni.showLoading({ title: '刷新中...' })
    await tabBarManager.refreshBadgeData()
    uni.hideLoading()
    uni.showToast({
      title: '刷新成功',
      icon: 'success'
    })
  } catch (error) {
    uni.hideLoading()
    uni.showToast({
      title: '刷新失败',
      icon: 'error'
    })
  }
}

// 设置角标
const setBadge = async (tabName, count) => {
  try {
    await tabBarManager.setBadge(tabName, count)
    uni.showToast({
      title: `${tabName} 角标设置为 ${count}`,
      icon: 'success'
    })
  } catch (error) {
    uni.showToast({
      title: '设置失败',
      icon: 'error'
    })
  }
}

// 增加角标
const incrementBadge = async (tabName) => {
  try {
    await tabBarManager.incrementBadge(tabName)
    uni.showToast({
      title: `${tabName} 角标 +1`,
      icon: 'success'
    })
  } catch (error) {
    uni.showToast({
      title: '操作失败',
      icon: 'error'
    })
  }
}

// 减少角标
const decrementBadge = async (tabName) => {
  try {
    await tabBarManager.decrementBadge(tabName)
    uni.showToast({
      title: `${tabName} 角标 -1`,
      icon: 'success'
    })
  } catch (error) {
    uni.showToast({
      title: '操作失败',
      icon: 'error'
    })
  }
}

// 显示小红点
const showDot = async (tabName) => {
  try {
    await tabBarManager.showDot(tabName)
    uni.showToast({
      title: `${tabName} 显示红点`,
      icon: 'success'
    })
  } catch (error) {
    uni.showToast({
      title: '操作失败',
      icon: 'error'
    })
  }
}

// 清除角标
const clearBadge = async (tabName) => {
  try {
    await tabBarManager.clearBadge(tabName)
    uni.showToast({
      title: `${tabName} 角标已清除`,
      icon: 'success'
    })
  } catch (error) {
    uni.showToast({
      title: '操作失败',
      icon: 'error'
    })
  }
}

// 清除所有角标
const clearAllBadges = async () => {
  try {
    await tabBarManager.clearAllBadges()
    uni.showToast({
      title: '所有角标已清除',
      icon: 'success'
    })
  } catch (error) {
    uni.showToast({
      title: '操作失败',
      icon: 'error'
    })
  }
}

// TabBar 切换事件
const onTabBarChange = ({ index, pagePath }) => {
  console.log('TabBar changed:', { index, pagePath })
}
</script>

<style scoped>
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding: 20rpx;
  padding-bottom: calc(100rpx + env(safe-area-inset-bottom)); /* 为自定义 TabBar 留出空间，适配苹果安全区域 */
}

.header {
  text-align: center;
  padding: 40rpx 0;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.badge-status {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.status-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
}

.status-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
}

.status-label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.status-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #007aff;
}

.operations {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
}

.operation-group {
  margin-bottom: 40rpx;
}

.operation-group:last-child {
  margin-bottom: 0;
}

.group-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #666;
  margin-bottom: 20rpx;
  display: block;
}

.operation-btn {
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 12rpx;
  font-size: 28rpx;
  margin-bottom: 20rpx;
  border: none;
  background-color: #f8f9fa;
  color: #333;
}

.operation-btn:last-child {
  margin-bottom: 0;
}

.operation-btn.small {
  width: 48%;
  height: 60rpx;
  line-height: 60rpx;
  font-size: 24rpx;
  margin-bottom: 15rpx;
}

.operation-btn.primary {
  background-color: #007aff;
  color: #fff;
}

.operation-btn.success {
  background-color: #28a745;
  color: #fff;
}

.operation-btn.warning {
  background-color: #ffc107;
  color: #333;
}

.operation-btn.info {
  background-color: #17a2b8;
  color: #fff;
}

.operation-btn.danger {
  background-color: #dc3545;
  color: #fff;
}

.btn-row {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 10rpx;
}
</style> 