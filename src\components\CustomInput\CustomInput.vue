<template>
  <view class="custom-input-wrapper" :class="{ 'input-disabled': disabled }">
    <!-- 前缀图标 -->
    <view v-if="prefixIcon" class="input-prefix">
      <text class="input-icon">{{ prefixIcon }}</text>
    </view>
    
    <!-- 输入框 -->
    <input
      v-if="!isTextarea"
      class="custom-input"
      :class="[
        `input-${border}`,
        {
          'input-focus': isFocused,
          'input-error': error
        }
      ]"
      :type="type"
      :value="modelValue"
      :placeholder="placeholder"
      :disabled="disabled"
      :maxlength="maxlength > 0 ? maxlength : undefined"
      :readonly="readonly"
      :style="inputStyle"
      @input="handleInput"
      @focus="handleFocus"
      @blur="handleBlur"
      @confirm="handleConfirm"
    />
    
    <!-- 多行文本框 -->
    <textarea
      v-else
      class="custom-textarea"
      :class="[
        `input-${border}`,
        {
          'input-focus': isFocused,
          'input-error': error
        }
      ]"
      :value="modelValue"
      :placeholder="placeholder"
      :disabled="disabled"
      :maxlength="maxlength > 0 ? maxlength : undefined"
      :readonly="readonly"
      :style="textareaStyle"
      @input="handleInput"
      @focus="handleFocus"
      @blur="handleBlur"
    />
    
    <!-- 后缀图标 -->
    <view v-if="suffixIcon" class="input-suffix" @click="handleSuffixClick">
      <text class="input-icon">{{ suffixIcon }}</text>
    </view>
    
    <!-- 清除按钮 -->
    <view v-if="clearable && modelValue && !disabled" class="input-clear" @click="handleClear">
      <text class="clear-icon">✕</text>
    </view>
  </view>
</template>

<script setup>
import { defineProps, defineEmits, computed, ref } from 'vue'

const props = defineProps({
  modelValue: {
    type: [String, Number],
    default: ''
  },
  type: {
    type: String,
    default: 'text' // text, number, password, tel, email
  },
  placeholder: {
    type: String,
    default: '请输入内容'
  },
  disabled: {
    type: Boolean,
    default: false
  },
  readonly: {
    type: Boolean,
    default: false
  },
  clearable: {
    type: Boolean,
    default: false
  },
  maxlength: {
    type: [String, Number],
    default: -1
  },
  border: {
    type: String,
    default: 'surround', // surround, bottom, none
    validator: (value) => ['surround', 'bottom', 'none'].includes(value)
  },
  prefixIcon: {
    type: String,
    default: ''
  },
  suffixIcon: {
    type: String,
    default: ''
  },
  error: {
    type: Boolean,
    default: false
  },
  height: {
    type: String,
    default: ''
  },
  width: {
    type: String,
    default: ''
  },
  isTextarea: {
    type: Boolean,
    default: false
  },
  rows: {
    type: [String, Number],
    default: 3
  }
})

const emit = defineEmits(['update:modelValue', 'focus', 'blur', 'confirm', 'clear', 'suffix-click'])

const isFocused = ref(false)

// 输入框样式
const inputStyle = computed(() => {
  const style = {}
  
  if (props.height) {
    style.height = props.height
  }
  
  if (props.width) {
    style.width = props.width
  }
  
  return style
})

// 文本域样式
const textareaStyle = computed(() => {
  const style = {}
  
  if (props.width) {
    style.width = props.width
  }
  
  if (props.rows) {
    style.minHeight = `${Number(props.rows) * 40}rpx`
  }
  
  return style
})

// 处理输入
const handleInput = (event) => {
  console.log("handleInput===", event)
  console.log("event.detail.value===", event.detail.value)
  const value = event.detail.value
  console.log("准备发送的值===", value)
  emit('update:modelValue', value)
}

// 处理聚焦
const handleFocus = (event) => {
  console.log("handleFocus===",event)
  isFocused.value = true
  emit('focus', event)
}

// 处理失焦
const handleBlur = (event) => {
    console.log("handleBlur===",event)
  isFocused.value = false
  emit('blur', event)
}

// 处理确认
const handleConfirm = (event) => {
  console.log("handleConfirm===",event)
  emit('confirm', event)
}

// 处理清除
const handleClear = () => {
  emit('update:modelValue', '')
  emit('clear')
}

// 处理后缀图标点击
const handleSuffixClick = () => {
  emit('suffix-click')
}
</script>

<style scoped>
.custom-input-wrapper {
  display: flex;
  align-items: center;
  position: relative;
  background: white;
  transition: all 0.3s ease;
}

.custom-input,
.custom-textarea {
  flex: 1;
  padding: 20rpx 24rpx;
  font-size: 28rpx;
  color: #000;
  background: transparent;
  border: none;
  outline: none;
  box-sizing: border-box;
  line-height: 1.4;
}

.custom-textarea {
  resize: none;
  min-height: 120rpx;
  padding: 20rpx 24rpx;
}

/* 边框样式 */
.input-surround {
  border: 2rpx solid #d9d9d9;
  border-radius: 8rpx;
}

.input-bottom {
  border-bottom: 2rpx solid #d9d9d9;
}

.input-none {
  border: none;
}

/* 聚焦状态 */
.input-focus.input-surround {
  border-color: #1890ff;
  box-shadow: 0 0 0 4rpx rgba(24, 144, 255, 0.1);
}

.input-focus.input-bottom {
  border-bottom-color: #1890ff;
}

/* 错误状态 */
.input-error.input-surround {
  border-color: #ff4d4f;
}

.input-error.input-bottom {
  border-bottom-color: #ff4d4f;
}

/* 禁用状态 */
.input-disabled {
  background: #f5f5f5;
  cursor: not-allowed;
}

.input-disabled .custom-input,
.input-disabled .custom-textarea {
  color: #999;
  cursor: not-allowed;
}

/* 占位符样式 */
.custom-input::placeholder,
.custom-textarea::placeholder {
  color: #999;
  font-size: 26rpx;
}

/* 图标样式 */
.input-prefix,
.input-suffix {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 16rpx;
  color: #666;
}

.input-suffix {
  cursor: pointer;
}

.input-icon {
  font-size: 32rpx;
}

/* 清除按钮 */
.input-clear {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40rpx;
  height: 40rpx;
  margin-right: 16rpx;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
}

.input-clear:active {
  background: rgba(0, 0, 0, 0.2);
}

.clear-icon {
  font-size: 20rpx;
  color: #666;
  font-weight: bold;
}

/* 响应式优化 */
@media (max-width: 750rpx) {
  .custom-input,
  .custom-textarea {
    font-size: 30rpx;
    padding: 24rpx;
  }
  
  .custom-input::placeholder,
  .custom-textarea::placeholder {
    font-size: 28rpx;
  }
}
</style>
