# 角标 API 使用说明

## 概述

本项目实现了完整的 TabBar 角标功能，支持数字角标和小红点显示，并提供了模拟 API 接口来管理角标数据。

## 功能特性

- ✅ 数字角标显示（超过99显示"99+"）
- ✅ 小红点模式
- ✅ 服务器数据同步
- ✅ 本地状态管理
- ✅ 便捷的 API 接口
- ✅ 登录时自动获取角标数据
- ✅ 任务操作后自动更新角标

## 文件结构

```
src/
├── api/
│   └── badge.js                    # 角标相关 API 接口
├── stores/
│   └── tabBar.js                   # Pinia 状态管理
├── utils/
│   └── tabBar.js                   # TabBar 工具类
├── components/
│   └── CustomTabBar/
│       └── CustomTabBar.vue        # 自定义 TabBar 组件
└── pages/
    ├── login/index.vue             # 登录页面（获取角标数据）
    ├── tasks/list/list.vue         # 巡查页面（更新角标）
    ├── tasks/publish/list.vue      # 整改页面（更新角标）
    ├── tasks/publish/index.vue     # 发布页面（更新角标）
    └── demo/badge-demo.vue         # 角标功能演示页面
```

## API 接口

### 1. 获取角标数据

```javascript
import * as BadgeApi from '@/api/badge'

// 获取所有角标数据
const result = await BadgeApi.getBadgeData()
// 返回格式：
{
  code: 200,
  message: '获取成功',
  data: {
    taskBadge: 5,      // 巡查角标
    publishBadge: 3,   // 整改角标
    createBadge: 0,    // 发布角标
    mineBadge: 2       // 我的角标
  }
}
```

### 2. 更新角标数据

```javascript
// 更新单个角标
const result = await BadgeApi.updateBadgeData('task', 'set', 5)
// 参数说明：
// - tabName: 'task' | 'publish' | 'create' | 'mine'
// - operation: 'set' | 'increment' | 'decrement' | 'clear'
// - count: 数量（可选）
```

### 3. 获取任务统计数据

```javascript
// 获取任务统计并更新相应角标
const result = await BadgeApi.getTaskStatistics()
```

### 4. 清除所有角标

```javascript
// 清除所有角标
const result = await BadgeApi.clearAllBadges()
```

## TabBar 工具类使用

### 初始化

```javascript
import tabBarManager from '@/utils/tabBar'

// 在页面的 onMounted 或 setup 中初始化
onMounted(() => {
  tabBarManager.init()
})
```

### 基本操作

```javascript
// 1. 获取角标数据（从服务器）
await tabBarManager.fetchBadgeData()

// 2. 刷新角标数据
await tabBarManager.refreshBadgeData()

// 3. 设置角标
await tabBarManager.setBadge('task', 5)        // 设置数字角标
await tabBarManager.setBadge('mine', true)     // 设置小红点

// 4. 增减角标
await tabBarManager.incrementBadge('task', 1)  // 增加1
await tabBarManager.decrementBadge('task', 1)  // 减少1

// 5. 清除角标
await tabBarManager.clearBadge('task')         // 清除单个
await tabBarManager.clearAllBadges()           // 清除所有

// 6. 显示小红点
await tabBarManager.showDot('mine')

// 7. 隐藏角标
await tabBarManager.hideBadge('task')

// 8. 获取角标数量
const count = tabBarManager.getBadge('task')
const total = tabBarManager.getTotalBadgeCount()
```

### 高级功能

```javascript
// 更新任务统计数据并设置相应角标
await tabBarManager.updateTaskStatistics()

// 不同步到服务器的本地操作
await tabBarManager.setBadge('task', 5, false)
```

## 在页面中使用

### 1. 登录页面

```javascript
// src/pages/login/index.vue
const loginSuccess = async () => {
  try {
    // 初始化 tabBarManager
    tabBarManager.init()
    
    // 获取角标数据
    await tabBarManager.fetchBadgeData()
    
    // 跳转到主页
    uni.switchTab({ url: '/pages/tasks/list/list' })
  } catch (error) {
    console.error('获取角标数据失败:', error)
  }
}
```

### 2. 任务列表页面

```javascript
// src/pages/tasks/list/list.vue
const getTasksList = async () => {
  try {
    // 获取任务列表
    const result = await TasksApi.getTasksList()
    list.value = result.data
    
    // 任务列表更新后，刷新角标数据
    await tabBarManager.refreshBadgeData()
  } catch (error) {
    console.error('获取任务列表失败:', error)
  }
}

const handleAccept = async (taskId) => {
  try {
    // 接受任务
    await TasksApi.acceptTask(taskId)
    
    // 任务操作成功后，刷新角标数据
    await tabBarManager.refreshBadgeData()
  } catch (error) {
    console.error('接受任务失败:', error)
  }
}
```

### 3. 发布任务页面

```javascript
// src/pages/tasks/publish/index.vue
const submitTask = async () => {
  try {
    // 提交任务
    await PublishApi.commit(formData)
    
    // 提交成功后，刷新角标数据
    await tabBarManager.refreshBadgeData()
  } catch (error) {
    console.error('提交任务失败:', error)
  }
}
```

## 状态管理

使用 Pinia 进行状态管理，可以在任何组件中访问角标状态：

```javascript
import { useTabBarStore } from '@/stores/tabBar'

const store = useTabBarStore()

// 响应式获取角标值
const taskBadge = computed(() => store.taskBadge)
const publishBadge = computed(() => store.publishBadge)
const createBadge = computed(() => store.createBadge)
const mineBadge = computed(() => store.mineBadge)

// 获取总角标数量
const totalBadges = computed(() => store.getTotalBadgeCount())
```

## 演示页面

访问 `/pages/demo/badge-demo` 可以查看完整的角标功能演示，包括：

- 实时显示当前角标状态
- 各种角标操作按钮
- API 调用测试
- 错误处理演示

## 最佳实践

### 1. 错误处理

```javascript
try {
  await tabBarManager.setBadge('task', 5)
} catch (error) {
  console.error('设置角标失败:', error)
  // 不影响用户体验，静默处理
}
```

### 2. 性能优化

```javascript
// 批量操作时，可以关闭服务器同步
await tabBarManager.setBadge('task', 5, false)
await tabBarManager.setBadge('publish', 3, false)
// 最后统一刷新
await tabBarManager.refreshBadgeData()
```

### 3. 页面生命周期

```javascript
onMounted(() => {
  // 初始化
  tabBarManager.init()
})

onShow(() => {
  // 页面显示时刷新角标
  tabBarManager.refreshBadgeData()
})
```

## 注意事项

1. **初始化**：每个使用角标的页面都需要调用 `tabBarManager.init()`
2. **异步操作**：所有 API 调用都是异步的，需要使用 `await` 或 `.then()`
3. **错误处理**：建议对所有 API 调用进行错误处理
4. **性能考虑**：频繁的角标更新可能影响性能，建议合理控制更新频率
5. **数据同步**：角标数据会自动同步到服务器，除非明确指定不同步

## 扩展功能

如需扩展更多角标功能，可以：

1. 在 `src/api/badge.js` 中添加新的 API 接口
2. 在 `src/utils/tabBar.js` 中添加新的工具方法
3. 在 `src/stores/tabBar.js` 中添加新的状态管理
4. 在 `src/components/CustomTabBar/CustomTabBar.vue` 中添加新的显示逻辑

## 故障排除

### 常见问题

1. **角标不显示**：检查是否调用了 `tabBarManager.init()`
2. **数据不同步**：检查网络连接和 API 接口
3. **状态不更新**：检查是否正确使用了响应式数据
4. **页面跳转问题**：确保 TabBar 页面在 `pages.json` 中正确配置

### 调试方法

1. 查看控制台日志
2. 使用演示页面测试功能
3. 检查网络请求
4. 验证状态管理数据
