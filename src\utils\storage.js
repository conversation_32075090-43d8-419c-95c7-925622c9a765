/**
 * 缓存数据优化
 * import storage from '@/utils/storage';
 * 使用方法 【
 *     一、设置缓存
 *         string    storage.set('k', 'string你好啊', 3600);   // 设置一个 1 小时的缓存
 *         json      storage.set('k', { "b": "3" }, 2);
 *         array     storage.set('k', [1, 2, 3]);
 *         boolean   storage.set('k', true);
 *     二、读取缓存
 *         默认值    storage.get('k')
 *         string    storage.get('k', '你好')
 *         json      storage.get('k', { "a": "1" })
 *     三、移除/清理
 *         移除: storage.remove('k');
 *         清理：storage.clear();
 * 】
 * @type {String}
 */

const postfix = '_expiry' // 缓存有效期后缀

const storage = {
  /**
   * 设置缓存
   * @param {string} key 键名
   * @param {any} value 键值
   * @param {number} [ttl=0] 缓存时间（单位：秒），默认为 0 不设置过期时间
   */
  set: (key, value, ttl = 0) => {
    uni.setStorageSync(key, value)
    if (ttl > 0) {
      const expiryTime = Math.floor(Date.now() / 1000) + ttl
      uni.setStorageSync(key + postfix, expiryTime.toString())
    } else {
      uni.removeStorageSync(key + postfix)
    }
  },

  /**
   * 获取缓存
   * @param {string} key 键名
   * @param {any} [defaultValue=false] 缓存不存在时的默认值
   * @returns {any} 返回缓存值或默认值
   */
  get: (key, defaultValue = false) => {
    const expiryTime = parseInt(uni.getStorageSync(key + postfix), 10)
    if (expiryTime && expiryTime < Math.floor(Date.now() / 1000)) {
      storage.remove(key) // 缓存过期，清除
      return defaultValue
    }
    const value = uni.getStorageSync(key)
    return value !== '' && value !== null && value !== undefined ? value : defaultValue
  },

  /**
   * 删除指定缓存
   * @param {string} key 键名
   */
  remove: (key) => {
    uni.removeStorageSync(key)
    uni.removeStorageSync(key + postfix)
  },

  /**
   * 清理所有缓存
   */
  clear: () => {
    uni.clearStorageSync()
  },
}

export default storage
