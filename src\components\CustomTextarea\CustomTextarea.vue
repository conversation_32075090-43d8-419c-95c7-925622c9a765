<template>
  <view class="custom-textarea-wrapper" :class="{ 'textarea-disabled': disabled }">
    <textarea
      class="custom-textarea"
      :class="[
        `textarea-${border}`,
        {
          'textarea-focus': isFocused,
          'textarea-error': error
        }
      ]"
      :value="modelValue"
      :placeholder="placeholder"
      :disabled="disabled"
      :maxlength="maxlength > 0 ? maxlength : undefined"
      :readonly="readonly"
      :rows="rows"
      :style="textareaStyle"
      @input="handleInput"
      @focus="handleFocus"
      @blur="handleBlur"
      @confirm="handleConfirm"
    />
    
    <!-- 字数统计 -->
    <view v-if="showWordLimit && maxlength > 0" class="word-count">
      <text class="count-text" :class="{ 'count-exceed': currentLength > maxlength }">
        {{ currentLength }}/{{ maxlength }}
      </text>
    </view>
  </view>
</template>

<script setup>
import { defineProps, defineEmits, computed, ref } from 'vue'

const props = defineProps({
  modelValue: {
    type: [String, Number],
    default: ''
  },
  placeholder: {
    type: String,
    default: '请输入内容'
  },
  disabled: {
    type: Boolean,
    default: false
  },
  readonly: {
    type: Boolean,
    default: false
  },
  maxlength: {
    type: [String, Number],
    default: -1
  },
  rows: {
    type: [String, Number],
    default: 3
  },
  border: {
    type: String,
    default: 'surround', // surround, bottom, none
    validator: (value) => ['surround', 'bottom', 'none'].includes(value)
  },
  error: {
    type: Boolean,
    default: false
  },
  showWordLimit: {
    type: Boolean,
    default: false
  },
  height: {
    type: String,
    default: ''
  },
  width: {
    type: String,
    default: ''
  },
  autoHeight: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'focus', 'blur', 'confirm'])

const isFocused = ref(false)

// 当前字符长度
const currentLength = computed(() => {
  return String(props.modelValue || '').length
})

// 文本域样式
const textareaStyle = computed(() => {
  const style = {}
  
  if (props.width) {
    style.width = props.width
  }
  
  if (props.height) {
    style.height = props.height
  } else if (!props.autoHeight) {
    // 根据行数计算高度
    const lineHeight = 40 // rpx
    const padding = 40 // rpx (上下各20rpx)
    style.minHeight = `${Number(props.rows) * lineHeight + padding}rpx`
  }
  
  return style
})

// 处理输入
const handleInput = (event) => {
  console.log("CustomTextarea handleInput===", event)
  console.log("CustomTextarea event.detail.value===", event.detail.value)
  let value = event.detail.value

  // 如果设置了最大长度，截取字符
  if (props.maxlength > 0 && value.length > props.maxlength) {
    value = value.slice(0, props.maxlength)
  }

  console.log("CustomTextarea 准备发送的值===", value)
  emit('update:modelValue', value)
}

// 处理聚焦
const handleFocus = (event) => {
  isFocused.value = true
  emit('focus', event)
}

// 处理失焦
const handleBlur = (event) => {
  isFocused.value = false
  emit('blur', event)
}

// 处理确认
const handleConfirm = (event) => {
  emit('confirm', event)
}
</script>

<style scoped>
.custom-textarea-wrapper {
  position: relative;
  background: white;
  transition: all 0.3s ease;
}

.custom-textarea {
  width: 100%;
  padding: 20rpx 24rpx;
  font-size: 28rpx;
  color: #000;
  background: transparent;
  border: none;
  outline: none;
  box-sizing: border-box;
  line-height: 1.6;
  resize: none;
  font-family: inherit;
}

/* 边框样式 */
.textarea-surround {
  border: 2rpx solid #d9d9d9;
  border-radius: 8rpx;
}

.textarea-bottom {
  border-bottom: 2rpx solid #d9d9d9;
}

.textarea-none {
  border: none;
}

/* 聚焦状态 */
.textarea-focus.textarea-surround {
  border-color: #1890ff;
  box-shadow: 0 0 0 4rpx rgba(24, 144, 255, 0.1);
}

.textarea-focus.textarea-bottom {
  border-bottom-color: #1890ff;
}

/* 错误状态 */
.textarea-error.textarea-surround {
  border-color: #ff4d4f;
}

.textarea-error.textarea-bottom {
  border-bottom-color: #ff4d4f;
}

/* 禁用状态 */
.textarea-disabled {
  background: #f5f5f5;
  cursor: not-allowed;
}

.textarea-disabled .custom-textarea {
  color: #999;
  cursor: not-allowed;
}

/* 占位符样式 */
.custom-textarea::placeholder {
  color: #999;
  font-size: 26rpx;
}

/* 字数统计 */
.word-count {
  position: absolute;
  bottom: 16rpx;
  right: 20rpx;
  pointer-events: none;
}

.count-text {
  font-size: 22rpx;
  color: #999;
}

.count-exceed {
  color: #ff4d4f;
}

/* 响应式优化 */
@media (max-width: 750rpx) {
  .custom-textarea {
    font-size: 30rpx;
    padding: 24rpx;
  }
  
  .custom-textarea::placeholder {
    font-size: 28rpx;
  }
  
  .count-text {
    font-size: 24rpx;
  }
}
</style>
