<template>
  <view class="container">
    <!-- 用户信息 -->
    <userInfo/>
    <!-- 任务列表区域 -->
    <view class="task-list">
      <!-- 任务卡片列表 -->
      <block v-if="list.length > 0">
        <!-- 自定义整改任务卡片 -->
        <view 
          v-for="task in list" 
          :key="task.id" 
          class="reform-task-card"
          @click="goToTaskDetail(task.id)"
        >
          <!-- 任务头部 -->
          <view class="task-header">
            <view class="task-header-avatar">
              <image class="task-header-avatar-img" src="/static/gai_icon.png" />
            </view>
            <view class="task-header-right">
              <view class="task-title">
                <span>整改任务</span>
              </view>
              <view class="task-desc">{{ task.taskTitle }}</view>
            </view>
          </view>

          <!-- 任务类型标签 -->
          <view class="task-type">整改任务</view>
          
          <!-- 额外信息区域 -->
          <view class="task-info-area">
            <!-- 任务ID -->
            <view class="task-info-item">
              <text class="info-label">执行人：</text>
              <text class="info-value">{{ task.user && task.user.nickName }}</text>
            </view>

            <!-- 创建时间 -->
            <view class="task-info-item">
              <text class="info-label">创建时间：</text>
              <text class="info-value">{{ formatDate(task.createdAt) }}</text>
            </view>

            <!-- 执行时间范围 -->
            <view class="time-range">
              要求执行时间：
              {{ formatDate(task.beginDate) }}
              至
              {{ formatDate(task.endDate) }}
            </view>
          </view>
          
          <view class="line"></view>
          
          <!-- 任务底部，状态和按钮 -->
          <view class="task-footer">
            <!-- 任务状态 -->
            <text class="task-status" :class="getStatusClass(task.status)">
              {{ TaskStatus.getDescription(task.status) }}
            </text>
            
            <!-- 操作按钮 -->
            <view class="action-btns">
              <button class="view-btn" @click.stop="goToTaskDetail(task.id)">查看详情</button>
            </view>
          </view>
        </view>
      </block>

      <!-- 空状态 -->
      <view v-else class="empty-state" v-if="!loading">
        <CustomEmpty mode="list" icon="/static/empty.jpg" title="暂无整改任务" description="当前没有需要处理的整改任务" />
      </view>

      <!-- 加载状态 -->
      <view class="load-more">
        <view v-if="loading" class="loading">
          <text>加载中...</text>
        </view>
        <text v-else-if="!hasMore && list.length > 0">没有更多数据了</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import {ref, onMounted} from 'vue'
import * as Api from '@/api/user/index'
import {onLoad , onReachBottom, onPullDownRefresh} from "@dcloudio/uni-app"
import userInfo from "@/components/userInfo/userInfo.vue"
import CustomEmpty from '@/components/CustomEmpty/CustomEmpty.vue'
import {formatDate, formatTimeAgo} from "@/utils/date"
import {TaskStatus} from "@/common/enum/taskStatus"

// Tab 列表及选中状态
const tabList = [
  {name: "所有", status: -1},
  {name: "待接受", status: 20},
  {name: "待执行", status: 30},
  {name: "进行中", status: 40},
  {name: "已完成", status: 50},
]
const activeTab = ref(0)

// 分页相关数据
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const hasMore = ref(true)
const loading = ref(false)
const list = ref([])

onLoad(() => {
  currentPage.value = 1 // 重置页码
  total.value = 0 // 重置总数
  getTasksList(tabList[activeTab.value].status)
})

// 获取任务列表
const getTasksList = async (status, isLoadMore = false) => {
  if (loading.value) return

  try {
    loading.value = true
    console.log('加载数据', { page: currentPage.value, status })

    const result = await Api.getCorrectionList({
      status,
      page: currentPage.value,
      pageSize: pageSize.value
    })

    if (result.code === 200) {
      const { list: newList, total: totalCount } = result.data
      
      // 过滤只保留整改任务
      const filteredList = newList.filter(task => task.taskType === 20)

      // 更新数据
      if (isLoadMore) {
        list.value = [...list.value, ...filteredList]
      } else {
        list.value = filteredList
      }

      // 更新总数和是否有更多数据
      total.value = totalCount
      hasMore.value = list.value.length < totalCount

      console.log('数据加载完成', {
        currentLength: list.value.length,
        total: totalCount,
        hasMore: hasMore.value
      })
    }
  } catch (error) {
    console.error('获取任务列表失败:', error)
    uni.showToast({
      title: '获取数据失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

// 监听页面触底事件
onReachBottom(() => {
  console.log('触发页面触底')
  if (!hasMore.value || loading.value) return

  currentPage.value++
  getTasksList(tabList[activeTab.value].status, true)
})

// 根据任务状态返回对应的样式类
const getStatusClass = (status) => {
  switch (status) {
    case TaskStatus.PENDING_ACCEPT:
      return 'status-pending'
    case TaskStatus.PENDING_EXECUTE:
      return 'status-waiting'
    case TaskStatus.IN_PROGRESS:
      return 'status-progress'
    case TaskStatus.COMPLETED:
      return 'status-completed'
    default:
      return ''
  }
}

// 跳转到任务详情页
const goToTaskDetail = (tasksId) => {
  uni.navigateTo({
    url: '/pages/user/publish/detail?tasksId=' + tasksId
  })
}

onMounted(() => {
  getTasksList(tabList[activeTab.value].status)
})

// 下拉刷新
onPullDownRefresh(()=>{
  currentPage.value = 1 // 重置页码
  total.value = 0 // 重置总数
  getTasksList(tabList[activeTab.value].status,false)
  setTimeout(function () {
    uni.stopPullDownRefresh();
  }, 1000);
})


</script>

<style scoped>
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

.bg-white {
  background-color: #ffffff;
}

.tabs {
  position: sticky;
  top: 0;
  z-index: 1;
  display: flex;
  justify-content: space-around;
  background-color: #fff;
  padding: 20rpx 0;
  margin-bottom: 20rpx;
}

.tab {
  position: relative;
  font-size: 28rpx;
  color: #666;
  padding: 10rpx 20rpx;
  text-align: center;
}

.tab.active {
  color: #007aff;
  font-weight: bold;
}

.underline {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #007aff;
  border-radius: 2rpx;
}

.task-list {
  padding: 20rpx;
}

.empty-state {
  padding: 40rpx;
  background-color: #fff;
  border-radius: 16rpx;
}

.load-more {
  text-align: center;
  padding: 30rpx 0;
  color: #999;
  font-size: 24rpx;
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 60rpx;
}

.loading::before {
  content: "";
  width: 30rpx;
  height: 30rpx;
  margin-right: 10rpx;
  border: 2px solid #999;
  border-top-color: transparent;
  border-radius: 50%;
  animation: loading 0.8s linear infinite;
}

@keyframes loading {
  to {
    transform: rotate(360deg);
  }
}

/* 自定义整改任务卡片样式 */
.reform-task-card {
  position: relative;
  background-color: #fff;
  padding: 30rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 30rpx;
}

.task-header {
  display: flex;
  flex-direction: row;
  margin-bottom: 16rpx;
  align-items: flex-start;
}

.task-header-avatar {
  width: 100rpx;
  height: 100rpx;
  overflow: hidden;
  flex-shrink: 0;
  border-radius: 100%;
}

.task-header-avatar-img {
  width: 100rpx;
  height: 100rpx;
  display: block;
}

.task-header-right {
  display: flex;
  padding-left: 20rpx;
  flex-direction: column;
  flex-grow: 1;
}

.task-title {
  height: 40rpx;
  line-height: 40rpx;
  font-size: 32rpx;
  font-weight: bold;
}

.task-date {
  font-size: 24rpx;
  color: #999;
  padding-left: 20rpx;
}

.task-desc {
  font-size: 28rpx;
  color: #333;
  margin-top: 10rpx;
}

.task-info-area {
  margin: 20rpx 0;
}

.task-info-item {
  display: flex;
  margin-bottom: 10rpx;
  font-size: 26rpx;
}

.info-label {
  color: #666;
  width: 150rpx;
}

.info-value {
  color: #333;
  flex: 1;
}

.task-type {
  position: absolute;
  font-size: 24rpx;
  color: #9cb9e0;
  font-weight: bold;
  right: 0;
  top: 0;
  padding: 16rpx 24rpx;
  background-color: #e5f2fa;
  border-radius: 0 0 0 20rpx;
}

.time-range {
  font-size: 26rpx;
  color: #b4b4b4;
  margin-bottom: 20rpx;
}

.task-footer {
  display: flex;
  align-items: center;
  margin-top: 30rpx;
  justify-content: space-between;
}

.task-status {
  font-size: 32rpx;
  font-weight: bold;
}

.status-pending {
  color: #ff9800;
}

.status-waiting {
  color: #2196f3;
}

.status-progress {
  color: #4caf50;
}

.status-completed {
  color: #607d8b;
}

.action-btns {
  display: flex;
}

.accept-btn, .view-btn, .execute-btn {
  min-width: 160rpx;
  padding: 0 30rpx;
  height: 70rpx;
  line-height: 70rpx;
  color: #fff;
  text-align: center;
  border-radius: 10rpx;
  font-size: 28rpx;
  border: none;
}

.accept-btn {
  background-color: #007aff;
}

.view-btn {
  background-color: #607d8b;
}

.execute-btn {
  background-color: #4caf50;
}

.line {
  width: 100%;
  height: 1px;
  background-color: #eee;
  margin: 20rpx 0;
}
</style>

