'use strict';

// 文档地址：https://ask.dcloud.net.cn/article/40283
const uniPush = uniCloud.getPushManager({appId:"__UNI__120B055"}) //注意这里需要传入你的应用appId  
exports.main = async (event, context) => {
	let obj = JSON.parse(event.body)
	return await uniPush.sendMessage({
		"push_clientid": obj.cids,  // 填写上一步在uni-app客户端获取到的客户端推送标识push_clientid  
		"force_notification":true,  // 填写true，客户端就会对在线消息自动创建“通知栏消息”。  
		"title":  obj.title,      
		"content": obj.content,  
		"payload": obj.payload,  
		"category": {  
			// HarmonyOS NEXT系统（纯血鸿蒙、非安卓鸿蒙）的消息分类，要给鸿蒙设备推送时才必传  
			"harmony":"MARKETING"  
		},  
		"options":{  
			"HW": {      
				 // 值为int 类型。1 表示华为测试消息，华为每个应用每日可发送该测试消息500条。此 target_user_type 参数请勿发布至线上。      
				  "/message/android/target_user_type":1      
			  } ,    
			"HO": {      
				 //值为int 类型。1 表示测试推送，不填默认为0。荣耀每个应用每日可发送该测试消息1000条。此测试参数请勿发布至线上。  
				  "/android/targetUserType": 1   
			  } ,  
			"VV": {      
				 //值为int 类型。0 表示正式推送；1 表示测试推送，不填默认为0。此 pushMode 参数请勿发布至线上。  
				  "/pushMode":1      
			  } ,    
			"XM": {      
				 //新小米消息分类下，私信公信id都必须要传，否则请求小米厂商接口会被拦截  
				  "/extra.channel_id": "填写小米平台申请的渠道id"   
			  }    
		}  
	})  
}
