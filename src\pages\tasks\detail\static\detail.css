page {
    width: 100%;
    height: 100%;
    overflow: auto;
}

page::-webkit-scrollbar {
    display: none;
}

.container {
    width: 740rpx;
    margin: 0rpx auto 0;
    padding: 20rpx 10rpx 0;
    background: #ffffff;
    border-radius: 10rpx;
    position: relative;
    box-sizing: border-box;
}

.tasks-info {
    width: 100%;
    position: relative;
    font-size: 16px;
}

.tasks-info-top {
    width: 100%;
    color: #585858;
    display: flex;
    align-content: center;
}

.tasks-info-top .task-title {
    width: 240rpx;
    height: 50rpx;
    line-height: 50rpx;
}

.tasks-info-top .task-title .task-date {
    font-size: 12px;
    color: #999;
    padding-left: 10rpx;
}

.tasks-info-top .tasks-no {
    flex: 1;
    height: 50rpx;
    line-height: 50rpx;
}

.task-desc {
    height: 50rpx;
    line-height: 50rpx;
    margin-top: 10rpx;
}

.location-list {
    margin-top: 40rpx;
}

.tasks-card {
    position: relative;
    background-color: #efefef;
    padding: 18rpx;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 16px;
}

.tasks-type {
    position: absolute;
    font-size: 12px;
    color: #9cb9e0;
    font-weight: bold;
    right: 0;
    top: 20;
    padding: 8px 12px;
    background-color: #e5f2fa;
    border-radius: 0 0 0 10px;
}

.point-content {
    margin-top: 20rpx;
    color: #424242;
}

.points-title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 10rpx;
}

.points-index {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(90rpx, 1fr));
    gap: 10rpx;
}

/* 添加包装器样式 */
.point-wrapper {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-bottom: 16rpx;
}

/* 保持原有圆圈样式不变 */
.point-circle {
    width: 85rpx;
    height: 85rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    font-size: 14px;
    font-weight: bold;
    color: #333;
    text-align: center;
    border: 1px solid #cbcbcb;
}

/* 添加底部指示点样式 */
.point-indicator {
    position: absolute;
    bottom: 0;
    width: 12rpx;
    height: 12rpx;
    border-radius: 50%;
    background-color: #007AFF;
}

.points-name {
    font-size: 14px;
    color: #666;
    margin-bottom: 8px;
}

.points-index-tag {
    width: 40rpx;
    height: 40rpx;
    line-height: 40rpx;
    float: left;
    font-size: 18rpx;
    background: #7db342;
    border-radius: 100%;
    text-align: center;
    color: white;
    box-sizing: border-box;
    margin: auto 10rpx auto auto;
}

.point-index-title {
    display: flex;
    align-items: center;
    gap: 10rpx;
    line-height: 40rpx;
    font-size: 30rpx;
}

.rate {
   border: 1px solid red;
    padding: 0 10rpx;
    border-radius: 10rpx;
    text-decoration: underline;
    text-underline: #ce8a80;
}
.rate-red {
    color: red;
}
.points-choices {
    display: flex;
    gap: 12px;
}

.problem_desc-wap {
    margin: 15rpx 0;
}

.problem_desc-input {
    background: white;
}

.points-btn-wap {
    width: 100%;
    height: 60px;
    margin-top: 20rpx;
    position: relative;
}

.points-btn-wap .points-btn {
    width: 200rpx;
    height: 70rpx;
    position: absolute;
    right: 10rpx;
    top: 0;
    bottom: 0;
    margin: auto;
    border-radius: 70rpx;
}

.btn-group {
    padding-top: 20rpx;
    padding-bottom: 80rpx;
}

.btn-finish, .btn-publish {
    width: 600rpx;
    height: 80rpx;
    margin: 20rpx auto;
    border-radius: 60rpx;
}

/* 未完成状态 */
.point-circle.bg-notdone {
    background: #e3e3e3;
    color: #666;
    border-color: #cbcbcb;
}

/* 已完成状态 */
.point-circle.bg-done {
    background: #7db342;
    color: #fff;
    border-color: #7db342;
}

.point-circle.bg-error {
    background: #ce8a80;
    color: #fff;
    border-color: #ce8a80;
}


/* 选中状态 - 放在最后以确保优先级最高 */
.point-circle.bg-act {
    background: #ce8a80;
    color: #fff;
    border-color: #ce8a80;
}

