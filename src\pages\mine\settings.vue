<template>
  <view class="settings-container">
    <view class="menu-list">
		<view class="menu-item" @click="copyClientId">
		  <text class="menu-text">ClientID</text>
		  <text class="cache-size">{{ clientId }}</text>
		</view>
      <view class="menu-item" @click="clearCache">
        <text class="menu-text">清除缓存</text>
        <text class="cache-size">{{ cacheSize }}</text>
      </view>
      <view class="menu-item">
        <text class="menu-text">当前版本</text>
        <text class="version">{{ version }}</text>
      </view>
    </view>

    <!-- 按钮区域 -->
    <view class="button-group">
      <!-- 退出登录按钮 -->
      <button class="logout-btn" type="default" @click="handleLogout">
        退出登录
      </button>
      
      <!-- 注销账号按钮 -->
      <button class="delete-account-btn" type="default" @click="handleDeleteAccount">
        注销账号
      </button>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()
const cacheSize = ref('0.00MB')
const version = ref('v1.0.0')


onMounted(() => {
  getCacheSize()
  getAppVersion() // 获取版本号
  getClientId()   // 用户ID
})

// 获取缓存大小
const getCacheSize = () => {
  // #ifdef H5
  const size = (localStorage.length * 2) / 1024 // 粗略计算，每个字符约2字节
  cacheSize.value = size.toFixed(2) + 'KB'
  // #endif
  
  // #ifdef APP-PLUS
  plus.cache.calculate((size) => {
    const sizeInMB = (size / (1024 * 1024)).toFixed(2)
    cacheSize.value = sizeInMB + 'MB'
  })
  // #endif
}

const clientId = ref("")
const getClientId = () =>{
	uni.getPushClientId({
		success: (res) => {
			clientId.value = res.cid
		},
		fail(err) {
			console.log(err)
			clientId.value = JSON.stringify(err)
		}
	})
}
// 复制
const copyClientId = ()=>{
	if(clientId.value == "")
	return
	uni.setClipboardData({
	    data: clientId.value, // e是你要保存的内容
	    success: function () {
			uni.showToast({
				title:'复制成功',
				icon:'none'
			})
	    }
	})
}

// 清除缓存
const clearCache = () => {
  uni.showModal({
    title: '提示',
    content: '确定要清除缓存吗？',
    success: (res) => {
      if (res.confirm) {
        // #ifdef H5
        localStorage.clear()
        // #endif
        
        // #ifdef APP-PLUS
        plus.cache.clear(() => {
          uni.showToast({
            title: '清除成功',
            icon: 'success'
          })
        })
        // #endif
        
        getCacheSize()
      }
    }
  })
}

// 处理退出登录
const handleLogout = () => {
  uni.showModal({
    title: '提示',
    content: '确定要退出登录吗？',
    success: (res) => {
      if (res.confirm) {
        userStore.logout()
        uni.reLaunch({
          url: '/pages/login/index'
        })
      }
    }
  })
}

// 处理注销账号
// const handleDeleteAccount = () => {
//   // 第一次确认
//   uni.showModal({
//     title: '警告',
//     content: '注销账号将永久删除您的所有数据，且无法找回，确定要继续吗？',
//     confirmColor: '#ff4d4f',
//     success: (res) => {
//       if (res.confirm) {
//         // 二次确认
//         uni.showModal({
//           title: '二次确认',
//           content: '请再次确认：您的账号数据将被永久删除，此操作不可逆转！',
//           confirmText: '确认注销',
//           confirmColor: '#ff4d4f',
//           success: (confirmRes) => {
//             if (confirmRes.confirm) {
//               // 执行注销账号逻辑
//               let res = userStore.deleteAccount()
//               uni.showToast({
//                 mask:true,
//                 title: res.message
//               })
//
//               setTimeout(()=>{
//                 uni.redirectTo({
//                   url: '/pages/login/index'
//                 })
//               },1500)
//             }
//           }
//         })
//       }
//     }
//   })
// }

const handleDeleteAccount = async () => {
  uni.showModal({
    title: '警告',
    content: '注销账号将永久删除您的所有数据，且无法找回，确定要继续吗？',
    confirmColor: '#ff4d4f',
    success: (res) => {
      console.log('第一次确认结果：', res);
      if (res.confirm) {
        setTimeout(() => {
          uni.showModal({
            title: '二次确认',
            content: '请再次确认：您的账号数据将被永久删除，此操作不可逆转！',
            confirmText: '确认注销',
            confirmColor: '#ff4d4f',
            success: async (confirmRes) => {
              console.log('二次确认结果：', confirmRes);
              if (confirmRes.confirm) {
                uni.showLoading({ title: '正在注销...' });
                try {
                  let res = await userStore.deleteAccount();
                  uni.hideLoading();
                  uni.showToast({
                    mask: true,
                    title: res.message,
                  });
                  setTimeout(() => {
                    uni.redirectTo({
                      url: '/pages/login/index',
                    });
                  }, 1500);
                } catch (error) {
                  uni.hideLoading();
                  console.error('注销失败：', error);
                  uni.showToast({
                    title: '注销失败，请稍后重试',
                    icon: 'none',
                  });
                }
              }
            },
          })
        }, 100)
      }
    },
  })
}
// 获取版本号
const getAppVersion = () => {
  // #ifdef APP-PLUS
  plus.runtime.getProperty(plus.runtime.appid, (info) => {
    version.value = 'v' + info.version
  })
  // #endif

  // #ifdef H5 || MP-WEIXIN || MP-ALIPAY
  uni.getSystemInfo({
    success: (res) => {
      // 获取应用版本号
      const appVersion = res.appVersion || res.version || '1.0.0'
      version.value = 'v' + appVersion
    },
    fail: () => {
      version.value = 'v1.0.0' // 获取失败时使用默认版本号
    }
  })
  // #endif
}

</script>

<style scoped>
.settings-container {
  padding: 20rpx;
  min-height: 100vh;
  background-color: #f5f5f5;
  box-sizing: border-box;
}

.menu-list {
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 40rpx;
}

.menu-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 20rpx;
  border-bottom: 1px solid #f5f5f5;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-text {
  font-size: 28rpx;
  color: #333;
}

.cache-size, .version {
  font-size: 26rpx;
  color: #999;
}

.button-group {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.logout-btn {
  width: 90% !important;
  height: 88rpx;
  line-height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  color: #fff;
  background: #ff4d4f;
  border: none;
  margin-top: 60rpx;
}

.delete-account-btn {
  width: 90% !important;
  height: 88rpx;
  line-height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  color: #999;
  background: #f5f5f5;
  border: 1px solid #ddd;
  margin-top: 30rpx;
}

.logout-btn::after, .delete-account-btn::after {
  border: none;
}

/* 按钮点击效果 */
.logout-btn:active, .delete-account-btn:active {
  opacity: 0.8;
}
</style> 