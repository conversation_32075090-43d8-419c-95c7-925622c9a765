# 图片上传组件迁移指南

## 概述

本文档记录了从 `uploadImage` 组件迁移到新的 `ImageUploader` 组件的过程和变化。

## 迁移示例

### 原始代码 (使用 uploadImage)

```vue
<template>
  <uploadImage 
    required 
    class="upload-image" 
    v-model:fileList="formData.patrolImage" 
    title="请拍摄相关地址巡查照片（最多9张）"
  />
</template>

<script setup>
import uploadImage from '@/components/uploadImage/uploadImage.vue'

// 数据处理
const submitData = {
  patrolImage: formData.value.patrolImage.map(item => item.url)
}
</script>
```

### 新代码 (使用 ImageUploader)

```vue
<template>
  <ImageUploader 
    v-model="formData.patrolImage" 
    title="请拍摄相关地址巡查照片"
    :max-count="9"
    :required="true"
    @change="onImageChange"
  />
</template>

<script setup>
import ImageUploader from '@/components/ImageUploader/ImageUploader.vue'

// 图片变化处理
const onImageChange = (imageList) => {
  console.log('图片列表变化:', imageList)
  formData.value.patrolImage = imageList
}

// 数据验证和处理
const submitTask = async () => {
  // 检查是否有上传中的图片
  const uploadingImages = formData.value.patrolImage.filter(img => img.status === 'uploading')
  if (uploadingImages.length > 0) {
    uni.showToast({
      title: '请等待图片上传完成',
      icon: 'none'
    })
    return
  }
  
  // 检查是否有上传失败的图片
  const errorImages = formData.value.patrolImage.filter(img => img.status === 'error')
  if (errorImages.length > 0) {
    uni.showToast({
      title: '存在上传失败的图片，请重新上传',
      icon: 'none'
    })
    return
  }
  
  // 提交数据
  const submitData = {
    patrolImage: formData.value.patrolImage
      .filter(item => item.status === 'success' && item.url)
      .map(item => item.url)
  }
}
</script>
```

## 主要变化

### 1. 组件引入

```javascript
// 旧的
import uploadImage from '@/components/uploadImage/uploadImage.vue'

// 新的
import ImageUploader from '@/components/ImageUploader/ImageUploader.vue'
```

### 2. 属性变化

| 旧属性 | 新属性 | 说明 |
|--------|--------|------|
| `v-model:fileList` | `v-model` | 简化了双向绑定语法 |
| `title="xxx（最多9张）"` | `title="xxx"` + `:max-count="9"` | 分离了标题和数量限制 |
| `required` | `:required="true"` | 明确布尔值传递 |
| - | `@change="onImageChange"` | 新增变化监听事件 |

### 3. 数据格式

新组件的图片对象包含状态信息：

```javascript
// 新组件的数据格式
{
  path: 'temp://xxx.jpg',    // 本地临时路径
  url: 'https://xxx.jpg',    // 服务器URL
  status: 'success'          // 状态: uploading | success | error
}
```

### 4. 数据验证

新组件需要额外的状态验证：

```javascript
// 检查上传状态
const uploadingImages = imageList.filter(img => img.status === 'uploading')
const errorImages = imageList.filter(img => img.status === 'error')

// 提取成功的图片URL
const successUrls = imageList
  .filter(item => item.status === 'success' && item.url)
  .map(item => item.url)
```

## 优势

### 1. 更好的用户体验
- 实时显示上传状态（上传中、成功、失败）
- 更清晰的错误提示
- 响应式布局，适配移动端

### 2. 更强的功能
- 支持事件监听
- 更灵活的配置选项
- 更好的错误处理

### 3. 更好的代码质量
- 不依赖第三方UI库
- 使用Vue 3 Composition API
- 更好的类型安全

## 迁移检查清单

- [ ] 更新组件引入路径
- [ ] 修改组件属性配置
- [ ] 添加事件监听函数
- [ ] 更新数据验证逻辑
- [ ] 更新数据提取逻辑
- [ ] 测试上传功能
- [ ] 测试预览功能
- [ ] 测试删除功能
- [ ] 测试错误处理

## 注意事项

1. **数据兼容性**: 新组件的数据格式包含状态信息，需要相应调整数据处理逻辑
2. **状态检查**: 提交前需要检查图片上传状态，确保没有上传中或失败的图片
3. **事件处理**: 建议添加 `@change` 事件监听，以便实时响应图片列表变化
4. **错误处理**: 新组件提供更详细的错误状态，可以提供更好的用户反馈

## 已完成迁移的页面

- [x] `src/pages/tasks/publish/index.vue` - 任务发布页面
- [x] `src/pages/tasks/detail/detail-v2.vue` - 任务详情页面
- [x] `src/pages/tasks/detail/publish.vue` - 整改任务页面

### 任务详情页面特殊处理

任务详情页面包含3个图片上传组件：

1. **巡查图片** - 用于上传问题发现时的照片
2. **整改后图片** - 用于立即整改时上传整改完成的照片
3. **地点验证图片** - 用于验证当前位置的照片

每个组件都添加了相应的事件处理函数和状态验证逻辑。

### 整改任务页面特殊处理

整改任务页面包含2个图片上传组件：

1. **巡查图片** - 只读模式，显示之前上传的问题照片
2. **整改后图片** - 用于上传整改完成后的照片

该页面特别处理了数据初始化，将服务器返回的图片数据转换为新组件所需的格式。

## 待迁移的页面

如果项目中还有其他使用 `uploadImage` 组件的页面，可以参考本指南进行迁移。
