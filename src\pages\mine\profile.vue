<template>
  <view class="profile-container">
    <up-form>
      <up-form-item label="头像">
        <view class="avatar-wrapper">
          <image class="avatar" :src="userInfo.avatar || '/static/default-avatar.png'" mode="aspectFill"/>
        </view>
      </up-form-item>
      <up-form-item label="姓名">
        <text>{{ userInfo.nickname || '-' }}</text>
      </up-form-item>
      <up-form-item label="角色">
        <text>{{ userInfo.roleName || '-' }}</text>
      </up-form-item>
      <up-form-item label="所属院区">
        <text>{{ userInfo.deptName || '-' }}</text>
      </up-form-item>
      <up-form-item label="手机号">
        <text>{{ userInfo.mobile || '-' }}</text>
      </up-form-item>
    </up-form>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()
const userInfo = ref({})

onMounted(async () => {
  try {
    userInfo.value = await userStore.fetchUserInfo(true) // 强制刷新用户信息
  } catch (error) {
    console.error('获取用户信息失败', error)
  }
})
</script>

<style scoped>
.profile-container {
  padding: 20rpx;
}

.avatar-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
}
</style> 