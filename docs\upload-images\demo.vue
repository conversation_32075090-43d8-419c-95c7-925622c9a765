<template>
  <view class="demo-container">
    <view class="demo-header">
      <text class="demo-title">ImageUploader 组件演示</text>
    </view>
    
    <scroll-view class="demo-content" scroll-y>
      <!-- 基础用法 -->
      <view class="demo-section">
        <view class="section-title">1. 基础用法</view>
        <view class="section-desc">最多上传3张图片</view>
        <ImageUploader 
          v-model="basicImages"
          title="基础上传"
          :max-count="3"
          @change="onBasicChange"
        />
        <view class="result-display">
          <text class="result-title">当前图片数量: {{ basicImages.length }}</text>
        </view>
      </view>
      
      <!-- 必填验证 -->
      <view class="demo-section">
        <view class="section-title">2. 必填验证</view>
        <view class="section-desc">带有必填标识的上传组件</view>
        <ImageUploader 
          v-model="requiredImages"
          title="身份证照片"
          :max-count="2"
          :required="true"
          @change="onRequiredChange"
        />
      </view>
      
      <!-- 预设数据 -->
      <view class="demo-section">
        <view class="section-title">3. 预设数据</view>
        <view class="section-desc">组件初始化时已有图片数据</view>
        <ImageUploader 
          v-model="presetImages"
          title="产品图片"
          :max-count="6"
        />
        <button class="demo-btn" @click="resetPresetImages">重置预设数据</button>
      </view>
      
      <!-- 禁用状态 -->
      <view class="demo-section">
        <view class="section-title">4. 禁用状态</view>
        <view class="section-desc">只能查看，不能编辑</view>
        <ImageUploader 
          v-model="disabledImages"
          title="查看图片"
          :disabled="true"
        />
        <button class="demo-btn" @click="toggleDisabled">
          {{ isDisabled ? '启用编辑' : '禁用编辑' }}
        </button>
      </view>
      
      <!-- 自定义配置 -->
      <view class="demo-section">
        <view class="section-title">5. 自定义配置</view>
        <view class="section-desc">仅支持相机拍照，原图质量</view>
        <ImageUploader 
          v-model="customImages"
          title="高清拍照"
          :max-count="4"
          :quality="100"
          :source-type="['camera']"
          :size-type="['original']"
          @upload-success="onUploadSuccess"
          @upload-error="onUploadError"
        />
      </view>
      
      <!-- 事件监听 -->
      <view class="demo-section">
        <view class="section-title">6. 事件监听</view>
        <view class="section-desc">监听上传成功和失败事件</view>
        <ImageUploader 
          v-model="eventImages"
          title="事件测试"
          :max-count="5"
          @change="onEventChange"
          @upload-success="onUploadSuccess"
          @upload-error="onUploadError"
        />
        <view class="event-log">
          <text class="log-title">事件日志:</text>
          <view class="log-item" v-for="(log, index) in eventLogs" :key="index">
            <text class="log-time">{{ log.time }}</text>
            <text class="log-content">{{ log.content }}</text>
          </view>
        </view>
      </view>
      
      <!-- 数据展示 -->
      <view class="demo-section">
        <view class="section-title">7. 数据展示</view>
        <view class="data-display">
          <text class="data-title">所有图片数据:</text>
          <view class="data-content">
            <text>基础上传: {{ basicImages.length }}张</text>
            <text>必填验证: {{ requiredImages.length }}张</text>
            <text>预设数据: {{ presetImages.length }}张</text>
            <text>禁用状态: {{ disabledImages.length }}张</text>
            <text>自定义配置: {{ customImages.length }}张</text>
            <text>事件监听: {{ eventImages.length }}张</text>
          </view>
        </view>
      </view>
      
      <!-- 操作按钮 -->
      <view class="demo-section">
        <view class="section-title">8. 批量操作</view>
        <view class="button-group">
          <button class="demo-btn primary" @click="clearAllImages">清空所有图片</button>
          <button class="demo-btn" @click="showAllData">查看所有数据</button>
          <button class="demo-btn" @click="exportData">导出数据</button>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script setup>
import { ref, reactive } from 'vue'
import ImageUploader from '@/components/ImageUploader/ImageUploader.vue'

// 各种场景的图片数据
const basicImages = ref([])
const requiredImages = ref([])
const presetImages = ref([
  {
    url: 'https://picsum.photos/400/400?random=1',
    status: 'success'
  },
  {
    url: 'https://picsum.photos/400/400?random=2',
    status: 'success'
  }
])
const disabledImages = ref([
  {
    url: 'https://picsum.photos/400/400?random=3',
    status: 'success'
  }
])
const customImages = ref([])
const eventImages = ref([])

// 禁用状态控制
const isDisabled = ref(true)

// 事件日志
const eventLogs = ref([])

// 事件处理函数
const onBasicChange = (list) => {
  console.log('基础上传变化:', list)
}

const onRequiredChange = (list) => {
  console.log('必填验证变化:', list)
  if (list.length === 0) {
    addEventLog('必填验证: 请至少上传一张图片')
  }
}

const onEventChange = (list) => {
  addEventLog(`图片列表变化: 当前${list.length}张图片`)
}

const onUploadSuccess = ({ index, url, item }) => {
  console.log('上传成功:', url)
  addEventLog(`上传成功: 第${index + 1}张图片`)
  uni.showToast({
    title: '上传成功',
    icon: 'success'
  })
}

const onUploadError = ({ index, error, item }) => {
  console.error('上传失败:', error)
  addEventLog(`上传失败: ${error}`)
  uni.showToast({
    title: '上传失败',
    icon: 'none'
  })
}

// 工具函数
const addEventLog = (content) => {
  const now = new Date()
  const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`
  
  eventLogs.value.unshift({
    time,
    content
  })
  
  // 只保留最近10条日志
  if (eventLogs.value.length > 10) {
    eventLogs.value = eventLogs.value.slice(0, 10)
  }
}

// 操作函数
const resetPresetImages = () => {
  presetImages.value = [
    {
      url: 'https://picsum.photos/400/400?random=' + Date.now(),
      status: 'success'
    }
  ]
  addEventLog('重置预设数据完成')
}

const toggleDisabled = () => {
  isDisabled.value = !isDisabled.value
  addEventLog(`${isDisabled.value ? '禁用' : '启用'}编辑模式`)
}

const clearAllImages = () => {
  uni.showModal({
    title: '确认操作',
    content: '确定要清空所有图片吗？',
    success: (res) => {
      if (res.confirm) {
        basicImages.value = []
        requiredImages.value = []
        presetImages.value = []
        disabledImages.value = []
        customImages.value = []
        eventImages.value = []
        addEventLog('清空所有图片完成')
        uni.showToast({
          title: '清空完成',
          icon: 'success'
        })
      }
    }
  })
}

const showAllData = () => {
  const allData = {
    basicImages: basicImages.value,
    requiredImages: requiredImages.value,
    presetImages: presetImages.value,
    disabledImages: disabledImages.value,
    customImages: customImages.value,
    eventImages: eventImages.value
  }
  
  console.log('所有图片数据:', allData)
  
  uni.showModal({
    title: '数据详情',
    content: `共有${Object.values(allData).reduce((sum, arr) => sum + arr.length, 0)}张图片`,
    showCancel: false
  })
}

const exportData = () => {
  const allUrls = [
    ...basicImages.value,
    ...requiredImages.value,
    ...presetImages.value,
    ...disabledImages.value,
    ...customImages.value,
    ...eventImages.value
  ].filter(item => item.url).map(item => item.url)
  
  if (allUrls.length === 0) {
    uni.showToast({
      title: '暂无图片数据',
      icon: 'none'
    })
    return
  }
  
  // 这里可以实现导出功能，比如复制到剪贴板
  console.log('导出的图片URL:', allUrls)
  addEventLog(`导出${allUrls.length}张图片URL`)
  
  uni.showToast({
    title: '数据已输出到控制台',
    icon: 'success'
  })
}
</script>

<style scoped>
.demo-container {
  height: 100vh;
  background-color: #f5f5f5;
}

.demo-header {
  background-color: #007aff;
  padding: 40rpx 30rpx 30rpx;
  color: white;
}

.demo-title {
  font-size: 36rpx;
  font-weight: bold;
}

.demo-content {
  height: calc(100vh - 100rpx);
  padding: 30rpx;
}

.demo-section {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.section-desc {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 30rpx;
}

.result-display {
  margin-top: 20rpx;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
}

.result-title {
  font-size: 28rpx;
  color: #333;
}

.demo-btn {
  margin-top: 20rpx;
  padding: 20rpx 40rpx;
  background-color: #f0f0f0;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
}

.demo-btn.primary {
  background-color: #007aff;
  color: white;
}

.event-log {
  margin-top: 20rpx;
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  max-height: 400rpx;
  overflow-y: auto;
}

.log-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 15rpx;
}

.log-item {
  display: flex;
  margin-bottom: 10rpx;
  font-size: 24rpx;
}

.log-time {
  color: #999;
  margin-right: 20rpx;
  min-width: 120rpx;
}

.log-content {
  color: #666;
  flex: 1;
}

.data-display {
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
}

.data-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 15rpx;
}

.data-content {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.data-content text {
  font-size: 26rpx;
  color: #666;
}

.button-group {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.button-group .demo-btn {
  flex: 1;
  min-width: 200rpx;
  margin-top: 0;
}
</style>
