# 主管巡查系统

## 项目简介

主管巡查系统是一款基于uni-app开发的移动应用，旨在帮助主管人员高效管理和执行巡查工作。该系统支持多平台部署（H5、微信小程序等），为现场巡查工作提供全流程数字化解决方案。

## 核心功能

### 用户认证系统
- 登录/注册功能
- 用户信息管理与权限控制

### 任务管理系统
- 任务列表展示（所有、待接受、待执行、进行中、已完成）
- 任务详情查看
- 任务接受功能
- 任务执行与状态更新

### 巡查点管理
- 巡查点列表展示
- 质控点管理
- 巡查点提交与完成
- 巡查记录与数据统计

### 任务发布功能
- 新任务发布
- 整改任务发布
- 任务模板管理

### 个人中心
- 个人资料管理
- 系统设置
- 工作统计

## 技术栈

- **前端框架**：Vue 3 + uni-app
- **状态管理**：Pinia
- **UI组件库**：uview-plus
- **构建工具**：Vite
- **其他工具**：
  - dayjs（日期处理）
  - vue-i18n（国际化）
  - clipboard（剪贴板功能）

## 应用流程

1. 用户打开应用后，系统检查登录状态
2. 已登录用户直接进入任务列表页面，未登录用户跳转到登录页
3. 用户可以查看不同状态的任务列表
4. 用户可以接受待接受的任务
5. 用户可以查看任务详情并执行任务
6. 用户可以提交巡查点信息
7. 用户可以发布新的任务或整改任务
8. 用户可以在个人中心管理个人信息和系统设置

## 项目优势

- **高效工作流**：数字化巡查流程，提高工作效率
- **数据可追溯**：完整记录巡查过程，支持数据回溯
- **多平台支持**：一次开发，多端部署
- **离线功能**：支持部分离线操作，适应现场网络不稳定情况
- **灵活配置**：支持自定义巡查项目和质控点

## 适用场景

- 工程建设现场巡查
- 安全生产检查
- 质量管理巡检
- 设备设施巡检
- 环境保护监测

## 开发与部署

### 开发环境
```bash
# 安装依赖
npm install

# 开发模式运行
npm run dev:h5         # H5
npm run dev:mp-weixin  # 微信小程序
```

### 生产环境
```bash
# 构建生产版本
npm run build:h5         # H5
npm run build:mp-weixin  # 微信小程序
```

## 问题
popup内容无法滚动的问题
解决： node_modules\uview-plus\components\u-popup\u-popup.vue
注释掉 @touchmove.stop.prevent="noop"



