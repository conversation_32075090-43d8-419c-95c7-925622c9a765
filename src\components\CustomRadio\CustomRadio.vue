<template>
  <view class="custom-radio-group" :class="{ 'radio-disabled': disabled }">
    <view
      v-for="(option, index) in options"
      :key="index"
      class="radio-item"
      :class="[
        `radio-${placement}`,
        `radio-${shape}`,
        {
          'radio-checked': isChecked(option),
          'radio-disabled': disabled || option.disabled
        }
      ]"
      @click="handleSelect(option)"
    >
      <!-- 单选框图标 -->
      <view class="radio-icon" :class="{ 'icon-checked': isChecked(option) }">
        <!-- 勾选标记 -->
        <view v-if="isChecked(option)" class="radio-check">✓</view>
      </view>

      <!-- 标签文字 -->
      <view class="radio-label">
        <text>{{ option.label || option.name || option }}</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue'

const props = defineProps({
  modelValue: {
    type: [String, Number, Boolean],
    default: ''
  },
  options: {
    type: Array,
    default: () => [],
    required: true
  },
  placement: {
    type: String,
    default: 'column', // row, column
    validator: (value) => ['row', 'column'].includes(value)
  },
  shape: {
    type: String,
    default: 'round', // round, square
    validator: (value) => ['round', 'square'].includes(value)
  },
  disabled: {
    type: Boolean,
    default: false
  },
  valueKey: {
    type: String,
    default: 'value' // 当选项是对象时，指定值的键名
  },
  labelKey: {
    type: String,
    default: 'label' // 当选项是对象时，指定标签的键名
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

// 检查是否选中
const isChecked = (option) => {
  const value = getOptionValue(option)
  return value === props.modelValue
}

// 获取选项的值
const getOptionValue = (option) => {
  if (typeof option === 'object' && option !== null) {
    return option[props.valueKey] || option.value || option.name
  }
  return option
}

// 处理选择
const handleSelect = (option) => {
  if (props.disabled || option.disabled) {
    return
  }

  const value = getOptionValue(option)
  emit('update:modelValue', value)
  emit('change', value, option)
}
</script>

<style scoped>
.custom-radio-group {
  display: flex;
  gap: 20rpx;
}

.radio-row {
  flex-direction: row;
  flex-wrap: wrap;
}

.radio-column {
  flex-direction: column;
}

.radio-item {
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 16rpx;
  border-radius: 8rpx;
}

.radio-item:active {
  background: rgba(24, 144, 255, 0.1);
}

/* 单选框图标 */
.radio-icon {
  width: 36rpx;
  height: 36rpx;
  border: 2rpx solid #d9d9d9;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16rpx;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

/* 圆形样式 */
.radio-round .radio-icon {
  border-radius: 50%;
}

/* 方形样式 */
.radio-square .radio-icon {
  border-radius: 6rpx;
}

/* 选中状态的图标 */
.icon-checked {
  border-color: #1890ff;
  background: #1890ff;
}

/* 选中项的整体样式 */
.radio-item.radio-checked {
  background: rgba(24, 144, 255, 0.08);
  border-radius: 8rpx;
  padding: 8rpx 12rpx;
  margin: -8rpx -12rpx; /* 负边距抵消padding */
}

/* 选中项的文字样式 */
.radio-item.radio-checked .radio-label {
  color: #1890ff;
  font-weight: 500;
}

/* 勾选标记样式 */
.radio-check {
  color: white;
  font-size: 20rpx;
  font-weight: bold;
  line-height: 1;
  transform: scale(1.2);
}

/* 标签文字 */
.radio-label {
  flex: 1;
  font-size: 28rpx;
  color: #000;
  line-height: 1.4;
}

/* 选中状态 */
.radio-checked .radio-label {
  color: #1890ff;
  font-weight: 500;
}

/* 禁用状态 */
.radio-disabled {
  cursor: not-allowed;
  opacity: 0.8; /* 提高透明度，使内容更清晰 */
}

.radio-disabled:active {
  background: none;
}

/* 禁用状态下的未选中项 */
.radio-disabled .radio-icon {
  border-color: #d9d9d9;
  background: #f5f5f5;
}

/* 禁用状态下的选中项 - 保持较高的可见性 */
.radio-disabled .radio-icon.icon-checked {
  border-color: #1890ff !important;
  background: #1890ff !important;
  opacity: 0.8; /* 稍微降低透明度但保持可见 */
}

/* 禁用状态下的勾选标记 */
.radio-disabled .radio-check {
  color: white !important;
  opacity: 1; /* 勾选标记保持完全不透明 */
}

.radio-disabled .radio-label {
  color: #666; /* 提高文字对比度 */
}

/* 禁用状态下选中项的整体样式 */
.radio-disabled .radio-item.radio-checked {
  background: rgba(24, 144, 255, 0.1) !important; /* 淡蓝色背景 */
  border-radius: 8rpx;
  padding: 8rpx 12rpx;
  margin: -8rpx -12rpx; /* 负边距抵消padding */
}

/* 禁用状态下选中项的文字 */
.radio-disabled .radio-item.radio-checked .radio-label {
  color: #333 !important; /* 选中项文字更深色 */
  font-weight: 500; /* 稍微加粗 */
}

/* 整体禁用 */
.radio-disabled .radio-item {
  cursor: not-allowed;
}

/* 行布局时的间距调整 */
.radio-row .radio-item {
  margin-right: 40rpx;
  margin-bottom: 20rpx;
}

.radio-row .radio-item:last-child {
  margin-right: 0;
}

/* 列布局时的间距调整 */
.radio-column .radio-item {
  margin-bottom: 24rpx;
}

.radio-column .radio-item:last-child {
  margin-bottom: 0;
}

/* 响应式优化 */
@media (max-width: 750rpx) {
  .radio-label {
    font-size: 30rpx;
  }

  .radio-icon {
    width: 40rpx;
    height: 40rpx;
  }

  .radio-dot {
    width: 18rpx;
    height: 18rpx;
  }
}
</style>
