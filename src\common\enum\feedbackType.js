// 反馈类型
export const FeedbackType = {
    FULLY_COMPLIANT: 10,    // 完全符合
    PARTIALLY_COMPLIANT: 20, // 部分符合
    NON_COMPLIANT: 30,      // 不符合

    // 获取反馈类型描述
    getDescription(type) {
        switch (type) {
            case this.FULLY_COMPLIANT:
                return "完全符合"
            case this.PARTIALLY_COMPLIANT:
                return "部分符合"
            case this.NON_COMPLIANT:
                return "不符合"
            default:
                return "未知类型"
        }
    },

    getAll() {
        return [
            {value: this.FULLY_COMPLIANT, label: this.getDescription(this.FULLY_COMPLIANT)},
            {value: this.PARTIALLY_COMPLIANT, label: this.getDescription(this.PARTIALLY_COMPLIANT)},
            {value: this.NON_COMPLIANT, label: this.getDescription(this.NON_COMPLIANT)},
        ]
    }
}
