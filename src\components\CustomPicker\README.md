# CustomPicker 自定义选择器组件

## 概述

`CustomPicker` 是一个自定义的选择器组件，用于替换 uview-plus 的 `up-picker` 组件。它基于 uni-app 的 `picker-view` 组件实现，提供了底部弹出的多列选择功能。

## 功能特性

- ✅ 支持多列选择
- ✅ 支持自定义数据格式
- ✅ 支持自定义显示字段和值字段
- ✅ 底部弹出动画效果
- ✅ 支持取消和确认操作
- ✅ 响应式设计
- ✅ 符合项目设计规范

## 基础用法

### 1. 导入组件

```vue
<script setup>
import CustomPicker from '@/components/CustomPicker/CustomPicker.vue'
</script>
```

### 2. 单列选择

```vue
<template>
  <view>
    <button @click="showPicker = true">选择城市</button>
    
    <CustomPicker 
      :show="showPicker"
      title="请选择城市"
      :columns="[cityList]"
      @confirm="handleConfirm"
      @cancel="showPicker = false"
    />
  </view>
</template>

<script setup>
import { ref } from 'vue'

const showPicker = ref(false)
const cityList = ref([
  { label: '北京', value: 'beijing' },
  { label: '上海', value: 'shanghai' },
  { label: '广州', value: 'guangzhou' },
  { label: '深圳', value: 'shenzhen' }
])

const handleConfirm = (result) => {
  console.log('选择结果:', result.value[0])
  showPicker.value = false
}
</script>
```

### 3. 多列选择（省市区）

```vue
<template>
  <CustomPicker 
    :show="showAreaPicker"
    title="请选择地区"
    :columns="areaColumns"
    :defaultIndex="[0, 0, 0]"
    @confirm="handleAreaConfirm"
    @cancel="showAreaPicker = false"
    @change="handleAreaChange"
  />
</template>

<script setup>
const areaColumns = ref([
  // 省份列
  [
    { label: '北京市', value: 'beijing' },
    { label: '上海市', value: 'shanghai' },
    { label: '广东省', value: 'guangdong' }
  ],
  // 城市列
  [
    { label: '北京市', value: 'beijing_city' },
    { label: '朝阳区', value: 'chaoyang' },
    { label: '海淀区', value: 'haidian' }
  ],
  // 区县列
  [
    { label: '东城区', value: 'dongcheng' },
    { label: '西城区', value: 'xicheng' },
    { label: '朝阳区', value: 'chaoyang_district' }
  ]
])

const handleAreaConfirm = (result) => {
  const province = result.value[0]
  const city = result.value[1]
  const district = result.value[2]
  console.log('选择的地区:', province, city, district)
}

const handleAreaChange = (result) => {
  // 可以在这里实现联动逻辑
  console.log('选择变化:', result)
}
</script>
```

## API 参数

### Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| show | Boolean | false | 是否显示选择器 |
| title | String | '请选择' | 选择器标题 |
| columns | Array | [[]] | 选择器数据，二维数组 |
| keyName | String | 'label' | 显示字段名 |
| valueName | String | 'value' | 值字段名 |
| defaultIndex | Array | [0] | 默认选中的索引数组 |

### Events

| 事件名 | 说明 | 参数 |
|--------|------|------|
| confirm | 点击确认按钮时触发 | { value, index } |
| cancel | 点击取消按钮时触发 | - |
| change | 选择项发生变化时触发 | { value, index } |

### 事件参数说明

#### confirm 和 change 事件参数

```javascript
{
  value: [
    { label: '显示文本', value: '实际值', index: 0 },
    // ... 其他列的选择结果
  ],
  index: [0, 1, 2] // 各列选中的索引
}
```

## 数据格式

### 支持的数据格式

#### 1. 对象数组（推荐）
```javascript
const columns = [
  [
    { label: '选项1', value: 'option1' },
    { label: '选项2', value: 'option2' }
  ]
]
```

#### 2. 字符串数组
```javascript
const columns = [
  ['选项1', '选项2', '选项3']
]
```

#### 3. 自定义字段名
```vue
<CustomPicker 
  :columns="customColumns"
  keyName="name"
  valueName="id"
/>
```

```javascript
const customColumns = [
  [
    { name: '选项1', id: 1 },
    { name: '选项2', id: 2 }
  ]
]
```

## 使用示例

### 1. 时间选择器

```vue
<template>
  <CustomPicker 
    :show="showTimePicker"
    title="请选择时间"
    :columns="timeColumns"
    @confirm="handleTimeConfirm"
    @cancel="showTimePicker = false"
  />
</template>

<script setup>
const timeColumns = ref([
  // 小时
  Array.from({ length: 24 }, (_, i) => ({
    label: String(i).padStart(2, '0') + '时',
    value: i
  })),
  // 分钟
  Array.from({ length: 60 }, (_, i) => ({
    label: String(i).padStart(2, '0') + '分',
    value: i
  }))
])

const handleTimeConfirm = (result) => {
  const hour = result.value[0].value
  const minute = result.value[1].value
  console.log(`选择的时间: ${hour}:${minute}`)
}
</script>
```

### 2. 级联选择器

```vue
<template>
  <CustomPicker 
    :show="showCascadePicker"
    title="请选择分类"
    :columns="cascadeColumns"
    @change="handleCascadeChange"
    @confirm="handleCascadeConfirm"
    @cancel="showCascadePicker = false"
  />
</template>

<script setup>
const categories = {
  electronics: [
    { label: '手机', value: 'phone' },
    { label: '电脑', value: 'computer' }
  ],
  clothing: [
    { label: '上衣', value: 'top' },
    { label: '裤子', value: 'pants' }
  ]
}

const cascadeColumns = ref([
  [
    { label: '电子产品', value: 'electronics' },
    { label: '服装', value: 'clothing' }
  ],
  categories.electronics // 默认显示第一个分类的子项
])

const handleCascadeChange = (result) => {
  // 实现级联逻辑
  const firstColumnValue = result.value[0].value
  if (categories[firstColumnValue]) {
    cascadeColumns.value[1] = categories[firstColumnValue]
  }
}
</script>
```

### 3. 与表单结合使用

```vue
<template>
  <CustomForm :model="formData">
    <CustomFormItem label="选择城市" prop="city">
      <CustomInput 
        v-model="formData.cityName"
        placeholder="请选择城市"
        readonly
        @click="showCityPicker = true"
      />
    </CustomFormItem>
  </CustomForm>
  
  <CustomPicker 
    :show="showCityPicker"
    title="请选择城市"
    :columns="[cityList]"
    @confirm="handleCityConfirm"
    @cancel="showCityPicker = false"
  />
</template>

<script setup>
const formData = ref({
  cityId: '',
  cityName: ''
})

const handleCityConfirm = (result) => {
  formData.value.cityId = result.value[0].value
  formData.value.cityName = result.value[0].label
  showCityPicker.value = false
}
</script>
```

## 迁移指南

### 从 up-picker 迁移

**原代码：**
```vue
<up-picker
  :show="showPicker"
  :columns="[cityList]"
  keyName="label"
  @confirm="handleConfirm"
  @cancel="showPicker = false"
  title="选择城市"
/>
```

**新代码：**
```vue
<CustomPicker
  :show="showPicker"
  :columns="[cityList]"
  keyName="label"
  @confirm="handleConfirm"
  @cancel="showPicker = false"
  title="选择城市"
/>
```

### 主要变化

1. **组件名称**：`up-picker` → `CustomPicker`
2. **API 保持一致**：所有属性和事件名称保持不变
3. **样式增强**：更好的视觉效果和动画
4. **性能优化**：基于原生 picker-view 组件

## 样式定制

组件使用了项目统一的设计规范：

- **头部高度**：88rpx（移动端 96rpx）
- **内容高度**：500rpx（移动端 540rpx）
- **字体大小**：28rpx（移动端 30rpx）
- **主色调**：#1890ff
- **边框颜色**：#f0f0f0

### 自定义样式

```vue
<style>
:deep(.picker-header) {
  background: #f5f5f5;
}

:deep(.picker-title) {
  color: #1890ff;
  font-weight: bold;
}

:deep(.picker-item) {
  color: #333;
  font-size: 30rpx;
}
</style>
```

## 注意事项

1. **数据格式**：确保 columns 是二维数组格式
2. **索引同步**：defaultIndex 数组长度应与 columns 长度一致
3. **字段映射**：使用 keyName 和 valueName 指定显示和值字段
4. **事件处理**：记得在确认后关闭选择器
5. **级联选择**：在 change 事件中更新后续列的数据

## 最佳实践

1. **数据预处理**：提前准备好选择器数据，避免在组件中处理
2. **状态管理**：使用响应式数据管理选择器的显示状态
3. **用户体验**：提供清晰的标题和占位符文本
4. **错误处理**：处理数据为空或格式错误的情况
5. **性能优化**：对于大量数据，考虑虚拟滚动或分页加载
