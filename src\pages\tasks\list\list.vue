<template>
  <view class="container">
    <!-- 用户信息 -->
    <userInfo />
    <!-- Tab 导航部分 -->
    <view class="tabs">
      <view class="tab" :class="{ active: activeTab === index }" v-for="(tab, index) in tabList" :key="index"
        @click="selectTab(index, tab.status)">
        {{ tab.name }}
        <view class="underline" v-if="activeTab === index"></view>
      </view>
    </view>

    <!-- 任务列表区域 -->
    <view class="task-list">
      <!-- 任务卡片列表 -->
      <task-card v-if="list.length > 0" v-for="task in list"
                 :key="task.id" :task-info="task" @accept="handleAccept" />

      <!-- 空状态 -->
      <view v-else class="empty-state" v-if="!loading">
        <CustomEmpty mode="list" icon="/static/empty.jpg" title="暂无任务" description="当前没有符合条件的任务" />
      </view>

      <!-- 加载状态 -->
      <view class="load-more">
        <view v-if="loading" class="loading">
          <text>加载中...</text>
        </view>
        <text v-else-if="!hasMore && list.length > 0">没有更多数据了</text>
      </view>
    </view>

    <!-- 自定义 TabBar -->
    <CustomTabBar ref="customTabBar"/>
  </view>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance } from 'vue'
import * as TasksApi from '@/api/tasks'
import { onLoad, onShow, onReachBottom, onPullDownRefresh } from "@dcloudio/uni-app"
import TaskCard from '@/components/taskCard/TaskCard.vue'
import userInfo from "@/components/userInfo/userInfo.vue"
import CustomTabBar from '@/components/CustomTabBar/CustomTabBar.vue'
import CustomEmpty from '@/components/CustomEmpty/CustomEmpty.vue'
import tabBarManager from '@/utils/tabBar'

// Tab 列表及选中状态
const tabList = [
  { name: "所有", status: -1 },
  { name: "待接受", status: 20 },
  { name: "待执行", status: 30 },
  { name: "进行中", status: 40 },
  { name: "已完成", status: 50 },
]
const activeTab = ref(0)

// 分页相关数据
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const hasMore = ref(true)
const loading = ref(false)
const list = ref([])

onMounted(() => {
  // 初始化 tabBarManager
  tabBarManager.init()
  getTasksList(tabList[activeTab.value].status)
})

// 页面显示时更新 TabBar 状态
const updateTabBarIndex = () => {
  // 通过 ref 获取 CustomTabBar 组件实例并更新索引
  const tabBarComponent = getCurrentInstance()?.refs?.customTabBar
  if (tabBarComponent && tabBarComponent.setCurrentIndex) {
    tabBarComponent.setCurrentIndex(0) // 巡查页面对应索引 0
  }
}

onShow(() => {
  currentPage.value = 1 // 重置页码
  total.value = 0 // 重置总数
  getTasksList(tabList[activeTab.value].status)
  
  // 更新 TabBar 状态
  updateTabBarIndex()
})

// 获取任务列表
const getTasksList = async (status, isLoadMore = false) => {
  if (loading.value) return

  try {
    loading.value = true
    console.log('加载数据', { page: currentPage.value, status })

    const result = await TasksApi.getTasksList({
      status,
      page: currentPage.value,
      pageSize: pageSize.value
    })

    if (result.code === 200) {
      const { list: newList, total: totalCount } = result.data

      // 更新数据
      if (isLoadMore) {
        list.value = [...list.value, ...newList]
      } else {
        list.value = newList
      }

      // 更新总数和是否有更多数据
      total.value = totalCount
      hasMore.value = list.value.length < totalCount

      console.log('数据加载完成', {
        currentLength: list.value.length,
        total: totalCount,
        hasMore: hasMore.value
      })

      // 任务列表更新后，刷新角标数据
      await tabBarManager.refreshBadgeData()
    }
  } catch (error) {
    console.error('获取任务列表失败:', error)
    uni.showToast({
      title: '获取数据失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

// 监听页面触底事件
onReachBottom(() => {
  console.log('触发页面触底')
  if (!hasMore.value || loading.value) return

  currentPage.value++
  getTasksList(tabList[activeTab.value].status, true)
})

// 切换 Tab
const selectTab = (index, status) => {
  if (loading.value) return

  activeTab.value = index
  currentPage.value = 1 // 重置页码
  total.value = 0 // 重置总数
  list.value = [] // 清空列表
  hasMore.value = true // 重置加载更多状态
  getTasksList(status)
}

// 接受任务的处理逻辑
const handleAccept = async (tasksId) => {
  console.log('接受任务：', tasksId)
  
  try {
    // 这里应该调用接受任务的 API
    // await TasksApi.acceptTask(tasksId)
    
    // 任务操作成功后，刷新角标数据
    await tabBarManager.refreshBadgeData()
    
    // 如果在《待接受》 tab, 则需要更新下数据
    const status = tabList[activeTab.value].status
    if (status === 20) {
      currentPage.value = 1 // 重置页码
      total.value = 0 // 重置总数
      getTasksList(status)
    }
  } catch (error) {
    console.error('接受任务失败:', error)
    uni.showToast({
      title: '操作失败',
      icon: 'none'
    })
  }
}

// 下拉刷新
onPullDownRefresh(() => {
  currentPage.value = 1 // 重置页码
  total.value = 0 // 重置总数
  getTasksList(tabList[activeTab.value].status, false)
  setTimeout(function () {
    uni.stopPullDownRefresh();
  }, 1000);
})


</script>

<style scoped>
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: calc(60px + env(safe-area-inset-bottom)); /* 为自定义 TabBar 留出空间，适配苹果安全区域 */
}

.bg-white {
  background-color: #ffffff;
}

.tabs {
  position: sticky;
  top: 0;
  z-index: 1;
  display: flex;
  justify-content: space-around;
  background-color: #fff;
  padding: 20rpx 0;
  margin-bottom: 20rpx;
}

.tab {
  position: relative;
  font-size: 28rpx;
  color: #666;
  padding: 10rpx 20rpx;
  text-align: center;
}

.tab.active {
  color: #007aff;
  font-weight: bold;
}

.underline {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background-color: #007aff;
  border-radius: 2rpx;
}

.task-list {
  padding: 20rpx;
}

.empty-state {
  padding: 40rpx;
  background-color: #fff;
  border-radius: 16rpx;
}

.load-more {
  text-align: center;
  padding: 30rpx 0;
  color: #999;
  font-size: 24rpx;
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 60rpx;
}

.loading::before {
  content: "";
  width: 30rpx;
  height: 30rpx;
  margin-right: 10rpx;
  border: 2px solid #999;
  border-top-color: transparent;
  border-radius: 50%;
  animation: loading 0.8s linear infinite;
}

@keyframes loading {
  to {
    transform: rotate(360deg);
  }
}
</style>
