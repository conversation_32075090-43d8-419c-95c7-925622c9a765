# 自定义 TabBar 使用说明

## 概述

本项目实现了一个支持数字角标功能的自定义 TabBar 组件，可以完全替代 uni-app 原生的 tabBar。

## 功能特性

- ✅ 完全自定义的 TabBar 样式
- ✅ 支持数字角标显示
- ✅ 支持小红点显示
- ✅ 角标数量超过 99 显示 "99+"
- ✅ 响应式设计，适配不同设备
- ✅ 状态管理，支持全局角标控制
- ✅ 便捷的 API 接口

## 文件结构

```
src/
├── components/
│   └── CustomTabBar/
│       └── CustomTabBar.vue          # 自定义 TabBar 组件
├── stores/
│   └── tabBar.js                     # TabBar 状态管理
├── utils/
│   └── tabBar.js                     # TabBar 工具类
└── pages/
    └── demo/
        └── tabbar-demo.vue           # 使用演示页面
```

## 使用方法

### 1. 在页面中引入组件

```vue
<template>
  <view class="page">
    <!-- 页面内容 -->
    <view class="content">
      <!-- 你的页面内容 -->
    </view>
    
    <!-- 自定义 TabBar -->
    <CustomTabBar @change="onTabChange" />
  </view>
</template>

<script setup>
import CustomTabBar from '@/components/CustomTabBar/CustomTabBar.vue'
import tabBarManager from '@/utils/tabBar'
import { onMounted } from 'vue'

onMounted(() => {
  // 初始化 tabBarManager
  tabBarManager.init()
})

const onTabChange = ({ index, pagePath }) => {
  console.log('Tab changed:', { index, pagePath })
}
</script>
```

### 2. 管理角标

#### 使用 tabBarManager（推荐）

```javascript
import tabBarManager from '@/utils/tabBar'

// 在 setup 中初始化
onMounted(() => {
  tabBarManager.init()
})

// 设置角标数量
tabBarManager.setBadge('task', 5)        // 巡查 tab 显示 5
tabBarManager.setBadge('publish', 3)     // 整改 tab 显示 3
tabBarManager.setBadge('create', 1)      // 发布 tab 显示 1
tabBarManager.setBadge('mine', 8)        // 我的 tab 显示 8

// 显示小红点（不显示数字）
tabBarManager.showDot('task')            // 巡查 tab 显示小红点

// 增加角标数量
tabBarManager.incrementBadge('task')     // 巡查 +1
tabBarManager.incrementBadge('publish', 2) // 整改 +2

// 减少角标数量
tabBarManager.decrementBadge('task')     // 巡查 -1
tabBarManager.decrementBadge('publish', 2) // 整改 -2

// 清除角标
tabBarManager.clearBadge('task')         // 清除巡查角标
tabBarManager.clearAllBadges()           // 清除所有角标

// 隐藏角标
tabBarManager.hideBadge('task')          // 隐藏巡查角标

// 获取角标数量
const taskBadgeCount = tabBarManager.getBadge('task')
const totalCount = tabBarManager.getTotalBadgeCount()
```

#### 使用 Pinia Store

```javascript
import { useTabBarStore } from '@/stores/tabBar'

const tabBarStore = useTabBarStore()

// 设置角标
tabBarStore.setTaskBadge(5)
tabBarStore.setPublishBadge(3)
tabBarStore.setCreateBadge(1)
tabBarStore.setMineBadge(8)

// 增减角标
tabBarStore.incrementBadge('task', 1)
tabBarStore.decrementBadge('task', 1)

// 清除角标
tabBarStore.clearBadge('task')
tabBarStore.clearAllBadges()
```

### 3. Tab 名称对应关系

| Tab 名称 | 对应页面 | 说明 |
|---------|---------|------|
| `task` | `pages/tasks/list/list` | 巡查 |
| `publish` | `pages/tasks/publish/list` | 整改 |
| `create` | `pages/tasks/publish/index` | 发布 |
| `mine` | `pages/mine/index` | 我的 |

## API 参考

### TabBarManager 方法

| 方法名 | 参数 | 说明 |
|-------|------|------|
| `init()` | - | 初始化 store |
| `setBadge(tabName, count)` | `tabName`: string, `count`: number\|boolean | 设置角标数量或显示红点 |
| `incrementBadge(tabName, count)` | `tabName`: string, `count`: number | 增加角标数量 |
| `decrementBadge(tabName, count)` | `tabName`: string, `count`: number | 减少角标数量 |
| `clearBadge(tabName)` | `tabName`: string | 清除指定角标 |
| `clearAllBadges()` | - | 清除所有角标 |
| `showDot(tabName)` | `tabName`: string | 显示小红点 |
| `hideBadge(tabName)` | `tabName`: string | 隐藏角标 |
| `getBadge(tabName)` | `tabName`: string | 获取角标数量 |
| `getTotalBadgeCount()` | - | 获取总角标数量 |

### CustomTabBar 组件属性

| 属性名 | 类型 | 默认值 | 说明 |
|-------|------|-------|------|
| `current` | Number | 0 | 当前选中的 tab 索引 |

### CustomTabBar 组件事件

| 事件名 | 参数 | 说明 |
|-------|------|------|
| `change` | `{ index, pagePath }` | tab 切换时触发 |

## 样式自定义

你可以通过修改 `CustomTabBar.vue` 中的样式来自定义 TabBar 的外观：

```scss
.custom-tab-bar {
  // TabBar 容器样式
  background-color: #ffffff;
  border-top: 1px solid #e5e5e5;
  
  .tab-item {
    // Tab 项样式
    
    .badge {
      // 角标样式
      background-color: #ff4757;
      
      &.badge-dot {
        // 小红点样式
      }
    }
  }
}
```

## 注意事项

1. 使用自定义 TabBar 后，需要在 `pages.json` 中移除原生 `tabBar` 配置
2. 在每个需要显示 TabBar 的页面中都要引入 `CustomTabBar` 组件
3. 记得在页面的 `onMounted` 中调用 `tabBarManager.init()` 初始化
4. 页面内容需要预留底部空间（约 60px）给 TabBar

### ⚠️ 常见问题解决

#### switchTab 错误

如果遇到 `switchTab:fail can not switch to no-tabBar page` 错误，有两种解决方案：

**方案一：保留隐藏的原生 tabBar（推荐）**

在 `pages.json` 中保留 tabBar 配置但设置为透明隐藏：

```json
{
  "tabBar": {
    "color": "rgba(0,0,0,0)",
    "selectedColor": "rgba(0,0,0,0)",
    "backgroundColor": "rgba(0,0,0,0)",
    "borderStyle": "white",
    "height": "0px",
    "list": [
      {
        "pagePath": "pages/tasks/list/list",
        "text": "",
        "iconPath": "static/tabbar/task.png",
        "selectedIconPath": "static/tabbar/task-active.png"
      }
      // ... 其他 tab
    ]
  }
}
```

**方案二：使用 reLaunch 跳转**

自定义 TabBar 组件已经内置了降级处理，会自动尝试：
1. `uni.switchTab()` - 首选方式
2. `uni.reLaunch()` - 降级方式
3. `uni.navigateTo()` - 最后尝试

## 演示页面

运行项目后，访问 `pages/demo/tabbar-demo` 页面可以看到完整的功能演示。

## 兼容性

- ✅ H5
- ✅ 微信小程序
- ✅ 支付宝小程序
- ✅ App
- ✅ 其他小程序平台 