<template>
  <view class="container">
    <!-- 用户信息 -->
    <userInfo/>

    <!-- 任务列表区域 -->
    <view class="task-list">
      <!-- 任务卡片列表 -->
      <task-card v-if="list.length > 0" v-for="task in list" :key="task.id" :task-info="task" @accept="handleAccept"/>

      <!-- 空状态 -->
      <view v-else class="empty-state">
        <CustomEmpty mode="list" icon="/static/empty.jpg" title="暂无发布任务" description="当前没有需要处理的发布任务" />
      </view>

    </view>

    <!-- 自定义 TabBar -->
    <CustomTabBar ref="customTabBar"/>
  </view>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance } from 'vue'
import * as TasksApi from '@/api/tasks'
import { onShow, onPullDownRefresh } from "@dcloudio/uni-app"
import TaskCard from '@/components/taskCard/TaskCard.vue'
import userInfo from "@/components/userInfo/userInfo.vue"
import CustomTabBar from '@/components/CustomTabBar/CustomTabBar.vue'
import CustomEmpty from '@/components/CustomEmpty/CustomEmpty.vue'
import tabBarManager from '@/utils/tabBar'

// 分页相关数据
const list = ref([])

onMounted(() => {
  // 初始化 tabBarManager
  tabBarManager.init()
})

// 页面显示时更新 TabBar 状态
const updateTabBarIndex = () => {
  // 通过 ref 获取 CustomTabBar 组件实例并更新索引
  const tabBarComponent = getCurrentInstance()?.refs?.customTabBar
  if (tabBarComponent && tabBarComponent.setCurrentIndex) {
    tabBarComponent.setCurrentIndex(1) // 整改页面对应索引 1
  }
}

onShow(() => {
  getTasksList()
  
  // 更新 TabBar 状态
  updateTabBarIndex()
})

// 获取任务列表
const getTasksList = async () => {
  try {
    // 获取整改列表
    const result = await TasksApi.publishList()
    list.value = result.data
    
    // 任务列表更新后，刷新角标数据
    await tabBarManager.refreshBadgeData()
  } catch (error) {
    console.error('获取整改列表失败:', error)
    uni.showToast({
      title: '获取数据失败',
      icon: 'none'
    })
  }
}

// 接受任务的处理逻辑
const handleAccept = (tasksId) => {
  console.log('接受任务：', tasksId)
  // getTasksList()
}

// 下拉刷新
onPullDownRefresh(() => {
  getTasksList()
  setTimeout(function () {
    uni.stopPullDownRefresh();
  }, 1000);
})
</script>

<style scoped>
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: calc(60px + env(safe-area-inset-bottom)); /* 为自定义 TabBar 留出空间，适配苹果安全区域 */
}

.task-list {
  padding: 20rpx;
}

.empty-state {
  padding: 40rpx;
  background-color: #fff;
  border-radius: 16rpx;
}

</style>
