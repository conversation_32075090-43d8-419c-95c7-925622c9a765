<template>
  <view class="contact-container">
    <!-- 页面头部 -->
    <view class="header">
      <image class="logo" src="@/static/logo.png"></image>
      <view class="company-name">医院主管巡查系统</view>
      <view class="company-slogan">提供专业的医疗质量管理解决方案</view>
    </view>
    
    <!-- 联系方式卡片 -->
    <view class="contact-card">
<!--      <view class="card-title">-->
<!--        <text class="title-icon">📱</text>-->
<!--        <text>联系方式</text>-->
<!--      </view>-->
      
      <!-- 电话联系 -->
<!--      <view class="contact-item" hover-class="item-hover" @click="makePhoneCall">-->
<!--        <text class="item-icon">📞</text>-->
<!--        <view class="item-content">-->
<!--          <view class="item-label">咨询热线</view>-->
<!--          <view class="item-value">************</view>-->
<!--        </view>-->
<!--        <view class="item-action">-->
<!--          <view class="action-btn">拨打</view>-->
<!--        </view>-->
<!--      </view>-->
      
      <!-- 技术支持 -->
<!--      <view class="contact-item" hover-class="item-hover" @click="makeSupportCall">-->
<!--        <text class="item-icon">🛠️</text>-->
<!--        <view class="item-content">-->
<!--          <view class="item-label">技术支持</view>-->
<!--          <view class="item-value">************</view>-->
<!--        </view>-->
<!--        <view class="item-action">-->
<!--          <view class="action-btn">拨打</view>-->
<!--        </view>-->
<!--      </view>-->
      
      <!-- 邮箱联系 -->
      <view class="contact-item" hover-class="item-hover" @click="sendEmail">
        <text class="item-icon">✉️</text>
        <view class="item-content">
          <view class="item-label">邮箱</view>
          <view class="item-value"><EMAIL></view>
        </view>
        <view class="item-action">
          <view class="action-btn">发送</view>
        </view>
      </view>
      
      <!-- 公司地址 -->
      <view class="contact-item address-item" hover-class="item-hover" @click="copyAddress">
        <text class="item-icon">🏢</text>
        <view class="item-content">
          <view class="item-label">公司地址</view>
          <view class="item-value">浙江省杭州市西湖区钱江浙商创投中心3幢408室</view>
        </view>
        <view class="item-action">
          <view class="action-btn">复制</view>
        </view>
      </view>
      
      <!-- 工作时间 -->
      <view class="contact-item">
        <text class="item-icon">🕘</text>
        <view class="item-content">
          <view class="item-label">工作时间</view>
          <view class="item-value">周一至周五 8:30-17:00</view>
          <view class="item-desc">节假日休息</view>
        </view>
      </view>
    </view>
    
    <!-- 社交媒体卡片 -->
<!--    <view class="social-card">-->
<!--      <view class="card-title">-->
<!--        <text class="title-icon">🌐</text>-->
<!--        <text>社交媒体</text>-->
<!--      </view>-->
<!--      -->
<!--      <view class="social-icons">-->
<!--        <view class="social-item" @click="openWechat">-->
<!--          <image class="social-icon" src="@/static/wechat.png" mode="aspectFit"></image>-->
<!--          <text>官方微信</text>-->
<!--        </view>-->
<!--        <view class="social-item" @click="openWeibo">-->
<!--          <image class="social-icon" src="@/static/weibo.png" mode="aspectFit"></image>-->
<!--          <text>官方微博</text>-->
<!--        </view>-->
<!--      </view>-->
<!--    </view>-->
<!--    -->
    <!-- 公司地图 -->
<!--    <view class="map-card">-->
<!--      <view class="card-title">-->
<!--        <text class="title-icon">🗺️</text>-->
<!--        <text>公司位置</text>-->
<!--      </view>-->
<!--      -->
<!--      <view class="map-container">-->
<!--        <image class="map-image" src="@/static/map.png" mode="aspectFill"></image>-->
<!--        <view class="map-overlay">-->
<!--          <view class="map-btn" @click="openMap">查看详细地图</view>-->
<!--        </view>-->
<!--      </view>-->
<!--    </view>-->
    
    <!-- 关于我们卡片 -->
    <view class="about-card" v-if="false">
      <view class="card-title">
        <text class="title-icon">ℹ️</text>
        <text>关于我们</text>
      </view>
      
      <view class="about-content">
        <view class="about-paragraph">
          我们是一家专注于医疗质量管理的科技公司，致力于为各级医院提供高效、智能的主管巡查系统解决方案。
        </view>
        <view class="about-paragraph">
          我们的系统帮助医院管理者实时监控医疗质量，提高医疗服务水平，确保患者安全。通过数字化、智能化的管理手段，实现医疗服务的规范化、标准化和精细化。
        </view>
        <view class="about-features">
          <view class="feature-item">
            <text class="feature-icon">✓</text>
            <text>实时巡查管理</text>
          </view>
          <view class="feature-item">
            <text class="feature-icon">✓</text>
            <text>问题跟踪处理</text>
          </view>
          <view class="feature-item">
            <text class="feature-icon">✓</text>
            <text>质量数据分析</text>
          </view>
          <view class="feature-item">
            <text class="feature-icon">✓</text>
            <text>移动端巡查工具</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 页脚 -->
    <view class="footer" v-if="false">
      <view class="copyright">© {{ currentYear }} 医院主管巡查系统 版权所有</view>
      <view class="icp">京ICP备12345678号</view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue';

// 当前年份
const currentYear = computed(() => new Date().getFullYear());

// 拨打咨询电话
const makePhoneCall = () => {
  uni.makePhoneCall({
    phoneNumber: '4001234567',
    success: () => {
      console.log('拨打电话成功');
    },
    fail: (err) => {
      console.error('拨打电话失败', err);
      uni.showToast({
        title: '拨打电话失败',
        icon: 'none'
      });
    }
  });
}

// 拨打技术支持电话
const makeSupportCall = () => {
  uni.makePhoneCall({
    phoneNumber: '4008889999',
    success: () => {
      console.log('拨打技术支持电话成功');
    },
    fail: (err) => {
      console.error('拨打电话失败', err);
      uni.showToast({
        title: '拨打电话失败',
        icon: 'none'
      });
    }
  });
}

// 发送邮件
const sendEmail = () => {
  // 尝试使用系统邮件应用
  try {
    // H5环境
    if (typeof window !== 'undefined' && window.location) {
      window.location.href = 'mailto:<EMAIL>';
    } else {
      // 小程序等其他环境
      uni.showToast({
        title: '邮箱已复制到剪贴板',
        icon: 'none'
      });
      uni.setClipboardData({
        data: '<EMAIL>',
        success: () => {
          console.log('邮箱复制成功');
        }
      });
    }
  } catch (e) {
    console.error('打开邮件应用失败', e);
    // 复制到剪贴板作为备选方案
    uni.setClipboardData({
      data: '<EMAIL>',
      success: () => {
        uni.showToast({
          title: '邮箱已复制到剪贴板',
          icon: 'none'
        });
      }
    });
  }
}

// 复制地址
const copyAddress = () => {
  const address = '北京市海淀区中关村科技园区8号楼A座15层';
  uni.setClipboardData({
    data: address,
    success: () => {
      uni.showToast({
        title: '地址已复制',
        icon: 'success'
      });
    }
  });
}

// 打开微信二维码
const openWechat = () => {
  uni.previewImage({
    current: 0,
    urls: ['/static/wechat-qr.png'],
    fail: () => {
      uni.showToast({
        title: '微信二维码不可用',
        icon: 'none'
      });
    }
  });
}

// 打开微博
const openWeibo = () => {
  // 在支持的平台上打开浏览器
  try {
    if (typeof window !== 'undefined' && window.location) {
      window.open('https://weibo.com/hospital-system');
    } else {
      uni.showToast({
        title: '请访问 weibo.com/hospital-system',
        icon: 'none'
      });
    }
  } catch (e) {
    console.error('打开微博失败', e);
    uni.showToast({
      title: '请访问 weibo.com/hospital-system',
      icon: 'none'
    });
  }
}

// 打开地图
const openMap = () => {
  const latitude = 39.9839;
  const longitude = 116.3199;
  const name = '医院主管巡查系统';
  const address = '北京市海淀区中关村科技园区8号楼';
  
  uni.openLocation({
    latitude,
    longitude,
    name,
    address,
    fail: () => {
      uni.showToast({
        title: '打开地图失败',
        icon: 'none'
      });
    }
  });
}
</script>

<style>
/* 页面容器 */
.contact-container {
  padding: 20rpx;
  background-color: #f8f8f8;
}

/* 页面头部 */
.header {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx 0;
}

.logo {
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  margin-bottom: 20rpx;
}

.company-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.company-slogan {
  font-size: 28rpx;
  color: #666;
  text-align: center;
}

/* 卡片通用样式 */
.contact-card,
.social-card,
.map-card,
.about-card {
  background-color: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  margin-bottom: 30rpx;
  padding: 30rpx;
  overflow: hidden;
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 20rpx;
}

.title-icon {
  margin-right: 10rpx;
  font-size: 36rpx;
}

/* 联系项样式 */
.contact-item {
  display: flex;
  padding: 24rpx 0;
  border-bottom: 1px solid #f5f5f5;
  position: relative;
}

.contact-item:last-child {
  border-bottom: none;
}

.item-hover {
  background-color: #f9f9f9;
}

.item-icon {
  font-size: 40rpx;
  margin-right: 20rpx;
  width: 60rpx;
  text-align: center;
}

.item-content {
  flex: 1;
}

.item-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 6rpx;
  font-weight: 500;
}

.item-value {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 6rpx;
}

.item-desc {
  font-size: 24rpx;
  color: #999;
}

.item-action {
  display: flex;
  align-items: center;
}

.action-btn {
  font-size: 24rpx;
  color: #5677fc;
  padding: 6rpx 20rpx;
  background-color: #f0f2fd;
  border-radius: 20rpx;
}

.action-btn:active {
  opacity: 0.8;
}

/* 地址项特殊样式 */
.address-item .item-value {
  max-width: 400rpx;
  line-height: 1.4;
}

/* 社交媒体卡片 */
.social-icons {
  display: flex;
  justify-content: space-around;
  padding: 20rpx 0;
}

.social-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.social-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 10rpx;
}

.social-item text {
  font-size: 24rpx;
  color: #666;
}

/* 地图卡片 */
.map-container {
  position: relative;
  height: 300rpx;
  border-radius: 12rpx;
  overflow: hidden;
}

.map-image {
  width: 100%;
  height: 300rpx;
}

.map-overlay {
  position: absolute;
  bottom: 20rpx;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
}

.map-btn {
  background-color: rgba(86, 119, 252, 0.9);
  color: #fff;
  font-size: 28rpx;
  padding: 12rpx 30rpx;
  border-radius: 30rpx;
}

/* 关于我们卡片 */
.about-content {
  color: #666;
  font-size: 28rpx;
  line-height: 1.6;
}

.about-paragraph {
  margin-bottom: 20rpx;
}

.about-features {
  display: flex;
  flex-wrap: wrap;
  margin-top: 20rpx;
}

.feature-item {
  width: 50%;
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.feature-icon {
  color: #5677fc;
  margin-right: 10rpx;
  font-weight: bold;
}

/* 页脚 */
.footer {
  text-align: center;
  padding: 40rpx 0;
  color: #999;
  font-size: 24rpx;
}

.copyright {
  margin-bottom: 10rpx;
}

.icp {
  font-size: 22rpx;
}
</style> 