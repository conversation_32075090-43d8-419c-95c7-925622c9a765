{
  "pages": [
    {
      "path": "pages/index/index",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/login/index",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/register/index",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/tasks/list/list",
      "style": {
        "navigationBarTitleText": "任务列表",
        "navigationStyle": "default",
        "enablePullDownRefresh": true
      }
    },
    {
      "path": "pages/tasks/publish/index",
      "style": {
        "navigationBarTitleText": "发布",
        "navigationStyle": "default"
      }
    },
    {
      "path": "pages/mine/index",
      "style": {
        "navigationBarTitleText": "我的",
        "navigationStyle": "default"
      }
    },
    {
      "path": "pages/test/test",
      "style": {
        "navigationBarTitleText": ""
      }
    },
    {
      "path": "pages/user/user",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/tasks/detail/detail",
      "style": {
        "navigationBarTitleText": "主管巡查系统"
      }
    },
    {
      "path": "pages/tasks/detail/detail-v2",
      "style": {
        "navigationBarTitleText": "主管巡查系统"
      }
    },
    {
      "path": "pages/tasks/detail/publish",
      "style": {
        "navigationBarTitleText": "主管巡查系统"
      }
    },
    {
      "path": "pages/tasks/publish/list",
      "style": {
        "navigationBarTitleText": "整改任务"
      }
    },
    {
      "path": "pages/mine/profile",
      "style": {
        "navigationBarTitleText": "个人资料",
        "navigationStyle": "default"
      }
    },
    {
      "path": "pages/mine/settings",
      "style": {
        "navigationBarTitleText": "系统设置",
        "navigationStyle": "default"
      }
    },
	{
	  "path": "pages/contact/index",
	  "style": {
	    "navigationBarTitleText": "联系我们",
	    "navigationStyle": "default"
	  }
	},
    {
      "path": "pages/contact/privacy",
      "style": {
        "navigationBarTitleText": "隐私政策",
        "navigationStyle": "default"
      }
    },
    {
      "path": "pages/user/publish/index",
      "style": {
        "navigationBarTitleText": "整改列表",
        "navigationStyle": "default"
      }
    },
    {
      "path": "pages/user/publish/detail",
      "style": {
        "navigationBarTitleText": "整改任务详情",
        "navigationStyle": "default"
      }
    },
    {
      "path": "pages/demo/badge-demo",
      "style": {
        "navigationBarTitleText": "角标功能演示",
        "navigationStyle": "default"
      }
    },
    {
      "path": "pages/demo/push-demo",
      "style": {
        "navigationBarTitleText": "推送功能演示",
        "navigationStyle": "default"
      }
    },
    {
      "path": "pages/demo/image-uploader-demo",
      "style": {
        "navigationBarTitleText": "推送功能演示",
        "navigationStyle": "default"
      }
    }
  ],
  "globalStyle": {
    "pageOrientation": "portrait",
    "navigationBarTextStyle": "black",
    "navigationBarTitleText": "主管巡查系统",
    "navigationBarBackgroundColor": "#F8F8F8",
    "backgroundColor": "#F8F8F8"
  },
  "easycom": {
    "autoscan": true,
    "custom": {
//      "^u--(.*)": "uview-plus/components/u-$1/u-$1.vue",
//      "^up-(.*)": "uview-plus/components/u-$1/u-$1.vue",
//      "^u-([^-].*)": "uview-plus/components/u-$1/u-$1.vue"
    }
  },
  "h5": {
    "titleNView": false,
    "router": {
      "mode": "hash",
      "base": "/"
    },
    "optimization": {
      "treeShaking": {
        "enable": true
      }
    }
  }
}
