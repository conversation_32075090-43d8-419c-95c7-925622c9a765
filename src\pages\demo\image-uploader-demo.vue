<template>
  <view class="demo-container">
    <!-- 导航栏 -->
    <view class="demo-header">
      <text class="demo-title">ImageUploader 组件演示</text>
      <text class="demo-subtitle">基于uniapp原生组件开发</text>
    </view>
    
    <scroll-view class="demo-content" scroll-y>
      <!-- 基础用法 -->
      <view class="demo-section">
        <view class="section-title">1. 基础用法</view>
        <view class="section-desc">最多上传3张图片，支持相册和相机</view>
        <ImageUploader 
          v-model="basicImages"
          title="基础上传"
          :max-count="3"
          @change="onBasicChange"
        />
        <view class="result-info">
          <text>当前图片数量: {{ basicImages.length }}</text>
        </view>
      </view>
      
      <!-- 必填验证 -->
      <view class="demo-section">
        <view class="section-title">2. 必填验证</view>
        <view class="section-desc">带有必填标识，用于重要证件上传</view>
        <ImageUploader 
          v-model="requiredImages"
          title="身份证照片"
          :max-count="2"
          :required="true"
          @change="onRequiredChange"
        />
        <view class="validation-tip" v-if="requiredImages.length === 0">
          <text class="tip-text">⚠️ 请至少上传一张身份证照片</text>
        </view>
      </view>
      
      <!-- 预设数据 -->
      <view class="demo-section">
        <view class="section-title">3. 预设数据</view>
        <view class="section-desc">组件初始化时已有图片数据</view>
        <ImageUploader 
          v-model="presetImages"
          title="产品图片"
          :max-count="6"
        />
        <view class="button-row">
          <button class="demo-btn" @click="resetPresetImages">重置预设数据</button>
          <button class="demo-btn secondary" @click="addPresetImage">添加预设图片</button>
        </view>
      </view>
      
      <!-- 禁用状态 -->
      <view class="demo-section">
        <view class="section-title">4. 禁用状态</view>
        <view class="section-desc">只能查看，不能编辑</view>
        <ImageUploader 
          v-model="disabledImages"
          title="查看图片"
          :disabled="isDisabled"
        />
        <button class="demo-btn" @click="toggleDisabled">
          {{ isDisabled ? '启用编辑' : '禁用编辑' }}
        </button>
      </view>
      
      <!-- 自定义配置 -->
      <view class="demo-section">
        <view class="section-title">5. 自定义配置</view>
        <view class="section-desc">仅支持相机拍照，高质量原图</view>
        <ImageUploader 
          v-model="customImages"
          title="高清拍照"
          :max-count="4"
          :quality="100"
          :source-type="['camera']"
          :size-type="['original']"
          @upload-success="onUploadSuccess"
          @upload-error="onUploadError"
        />
      </view>
      
      <!-- 事件监听 -->
      <view class="demo-section">
        <view class="section-title">6. 事件监听</view>
        <view class="section-desc">监听上传成功和失败事件</view>
        <ImageUploader 
          v-model="eventImages"
          title="事件测试"
          :max-count="5"
          @change="onEventChange"
          @upload-success="onUploadSuccess"
          @upload-error="onUploadError"
        />
        
        <!-- 事件日志 -->
        <view class="event-log" v-if="eventLogs.length > 0">
          <view class="log-header">
            <text class="log-title">事件日志</text>
            <button class="clear-log-btn" @click="clearLogs">清空</button>
          </view>
          <scroll-view class="log-content" scroll-y>
            <view class="log-item" v-for="(log, index) in eventLogs" :key="index">
              <text class="log-time">{{ log.time }}</text>
              <text class="log-message">{{ log.message }}</text>
            </view>
          </scroll-view>
        </view>
      </view>
      
      <!-- 数据统计 -->
      <view class="demo-section">
        <view class="section-title">7. 数据统计</view>
        <view class="stats-grid">
          <view class="stats-item">
            <text class="stats-number">{{ basicImages.length }}</text>
            <text class="stats-label">基础上传</text>
          </view>
          <view class="stats-item">
            <text class="stats-number">{{ requiredImages.length }}</text>
            <text class="stats-label">必填验证</text>
          </view>
          <view class="stats-item">
            <text class="stats-number">{{ presetImages.length }}</text>
            <text class="stats-label">预设数据</text>
          </view>
          <view class="stats-item">
            <text class="stats-number">{{ getTotalImages() }}</text>
            <text class="stats-label">总计图片</text>
          </view>
        </view>
      </view>
      
      <!-- 批量操作 -->
      <view class="demo-section">
        <view class="section-title">8. 批量操作</view>
        <view class="button-grid">
          <button class="demo-btn danger" @click="clearAllImages">清空所有</button>
          <button class="demo-btn" @click="showAllData">查看数据</button>
          <button class="demo-btn" @click="exportUrls">导出URL</button>
          <button class="demo-btn" @click="simulateError">模拟错误</button>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script setup>
import { ref } from 'vue'
import ImageUploader from '@/components/ImageUploader/ImageUploader.vue'

// 页面标题
uni.setNavigationBarTitle({
  title: 'ImageUploader Demo'
})

// 各种场景的图片数据
const basicImages = ref([])
const requiredImages = ref([])
const presetImages = ref([
  {
    url: 'https://picsum.photos/400/400?random=1',
    status: 'success'
  },
  {
    url: 'https://picsum.photos/400/400?random=2', 
    status: 'success'
  }
])
const disabledImages = ref([
  {
    url: 'https://picsum.photos/400/400?random=3',
    status: 'success'
  }
])
const customImages = ref([])
const eventImages = ref([])

// 控制状态
const isDisabled = ref(true)

// 事件日志
const eventLogs = ref([])

// 工具函数
const formatTime = () => {
  const now = new Date()
  return `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`
}

const addLog = (message) => {
  eventLogs.value.unshift({
    time: formatTime(),
    message
  })
  
  // 只保留最近20条日志
  if (eventLogs.value.length > 20) {
    eventLogs.value = eventLogs.value.slice(0, 20)
  }
}

// 事件处理函数
const onBasicChange = (list) => {
  console.log('基础上传变化:', list)
}

const onRequiredChange = (list) => {
  console.log('必填验证变化:', list)
  if (list.length === 0) {
    addLog('必填验证: 请至少上传一张图片')
  } else {
    addLog(`必填验证: 已上传${list.length}张图片`)
  }
}

const onEventChange = (list) => {
  addLog(`图片列表变化: 当前${list.length}张图片`)
}

const onUploadSuccess = ({ index, url, item }) => {
  console.log('上传成功:', url)
  addLog(`✅ 第${index + 1}张图片上传成功`)
  uni.showToast({
    title: '上传成功',
    icon: 'success',
    duration: 1500
  })
}

const onUploadError = ({ index, error, item }) => {
  console.error('上传失败:', error)
  addLog(`❌ 第${index + 1}张图片上传失败: ${error}`)
  uni.showToast({
    title: '上传失败',
    icon: 'none',
    duration: 2000
  })
}

// 操作函数
const resetPresetImages = () => {
  presetImages.value = [
    {
      url: `https://picsum.photos/400/400?random=${Date.now()}`,
      status: 'success'
    }
  ]
  addLog('重置预设数据完成')
  uni.showToast({
    title: '重置完成',
    icon: 'success'
  })
}

const addPresetImage = () => {
  if (presetImages.value.length >= 6) {
    uni.showToast({
      title: '已达到最大数量',
      icon: 'none'
    })
    return
  }
  
  presetImages.value.push({
    url: `https://picsum.photos/400/400?random=${Date.now()}`,
    status: 'success'
  })
  addLog('添加预设图片')
}

const toggleDisabled = () => {
  isDisabled.value = !isDisabled.value
  addLog(`${isDisabled.value ? '禁用' : '启用'}编辑模式`)
}

const getTotalImages = () => {
  return basicImages.value.length + 
         requiredImages.value.length + 
         presetImages.value.length + 
         disabledImages.value.length + 
         customImages.value.length + 
         eventImages.value.length
}

const clearAllImages = () => {
  uni.showModal({
    title: '确认操作',
    content: '确定要清空所有图片吗？此操作不可恢复。',
    confirmColor: '#ff4757',
    success: (res) => {
      if (res.confirm) {
        basicImages.value = []
        requiredImages.value = []
        presetImages.value = []
        disabledImages.value = []
        customImages.value = []
        eventImages.value = []
        addLog('🗑️ 清空所有图片完成')
        uni.showToast({
          title: '清空完成',
          icon: 'success'
        })
      }
    }
  })
}

const showAllData = () => {
  const allData = {
    basicImages: basicImages.value,
    requiredImages: requiredImages.value,
    presetImages: presetImages.value,
    disabledImages: disabledImages.value,
    customImages: customImages.value,
    eventImages: eventImages.value
  }
  
  console.log('所有图片数据:', allData)
  addLog('📊 查看所有数据 (已输出到控制台)')
  
  const totalCount = getTotalImages()
  uni.showModal({
    title: '数据统计',
    content: `当前共有 ${totalCount} 张图片\n详细数据已输出到控制台`,
    showCancel: false
  })
}

const exportUrls = () => {
  const allUrls = [
    ...basicImages.value,
    ...requiredImages.value,
    ...presetImages.value,
    ...disabledImages.value,
    ...customImages.value,
    ...eventImages.value
  ].filter(item => item.url && item.status === 'success')
   .map(item => item.url)
  
  if (allUrls.length === 0) {
    uni.showToast({
      title: '暂无可导出的图片',
      icon: 'none'
    })
    return
  }
  
  console.log('导出的图片URL:', allUrls)
  addLog(`📤 导出${allUrls.length}个图片URL`)
  
  // 这里可以实现复制到剪贴板等功能
  uni.showModal({
    title: '导出成功',
    content: `已导出 ${allUrls.length} 个图片URL\n数据已输出到控制台`,
    showCancel: false
  })
}

const simulateError = () => {
  addLog('⚠️ 模拟上传错误')
  onUploadError({
    index: 0,
    error: '网络连接超时',
    item: {}
  })
}

const clearLogs = () => {
  eventLogs.value = []
  uni.showToast({
    title: '日志已清空',
    icon: 'success'
  })
}
</script>

<style scoped>
.demo-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  width: 100%;
  overflow-x: hidden;
}

.demo-header {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  padding: 60rpx 20rpx 40rpx;
  text-align: center;
  color: white;
  width: 100%;
  box-sizing: border-box;
}

.demo-title {
  font-size: 36rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 10rpx;
  word-wrap: break-word;
}

.demo-subtitle {
  font-size: 24rpx;
  opacity: 0.8;
}

.demo-content {
  height: calc(100vh - 140rpx);
  padding: 20rpx;
  width: 100%;
  box-sizing: border-box;
}

.demo-section {
  background: white;
  border-radius: 20rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

.section-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 10rpx;
  word-wrap: break-word;
}

.section-desc {
  font-size: 24rpx;
  color: #7f8c8d;
  margin-bottom: 24rpx;
  line-height: 1.5;
  word-wrap: break-word;
}

.result-info {
  margin-top: 20rpx;
  padding: 16rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border-left: 6rpx solid #007aff;
  width: 100%;
  box-sizing: border-box;
}

.validation-tip {
  margin-top: 20rpx;
  padding: 16rpx;
  background: #fff3cd;
  border-radius: 12rpx;
  border-left: 6rpx solid #ffc107;
  width: 100%;
  box-sizing: border-box;
}

.tip-text {
  color: #856404;
  font-size: 24rpx;
  word-wrap: break-word;
}

.button-row {
  display: flex;
  gap: 16rpx;
  margin-top: 20rpx;
  flex-wrap: wrap;
}

.demo-btn {
  flex: 1;
  min-width: 140rpx;
  padding: 20rpx 24rpx;
  background: #007aff;
  color: white;
  border: none;
  border-radius: 12rpx;
  font-size: 26rpx;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.demo-btn.secondary {
  background: #6c757d;
}

.demo-btn.danger {
  background: #ff4757;
}

.event-log {
  margin-top: 24rpx;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  overflow: hidden;
  width: 100%;
  box-sizing: border-box;
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 20rpx;
  background: #f8f9fa;
  border-bottom: 2rpx solid #e9ecef;
}

.log-title {
  font-size: 26rpx;
  font-weight: bold;
  color: #495057;
  flex: 1;
}

.clear-log-btn {
  padding: 8rpx 16rpx;
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 8rpx;
  font-size: 22rpx;
  white-space: nowrap;
}

.log-content {
  max-height: 300rpx;
  width: 100%;
}

.log-item {
  display: flex;
  padding: 12rpx 20rpx;
  border-bottom: 1rpx solid #f1f3f4;
  width: 100%;
  box-sizing: border-box;
}

.log-time {
  color: #6c757d;
  font-size: 22rpx;
  margin-right: 16rpx;
  min-width: 100rpx;
  flex-shrink: 0;
}

.log-message {
  color: #495057;
  font-size: 24rpx;
  flex: 1;
  word-wrap: break-word;
  overflow: hidden;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
  width: 100%;
}

.stats-item {
  text-align: center;
  padding: 24rpx 16rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16rpx;
  color: white;
  min-width: 0;
}

.stats-number {
  display: block;
  font-size: 40rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.stats-label {
  font-size: 22rpx;
  opacity: 0.9;
  word-wrap: break-word;
}

.button-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
  width: 100%;
}
</style>
