<template>
  <view class="push-demo">
    <view class="demo-header">
      <text class="title">推送功能演示</text>
    </view>

    <view class="demo-section">
      <view class="section-title">设备信息</view>
      <view class="info-card">
        <view class="info-row">
          <text class="label">ClientID:</text>
          <text class="value">{{ clientId || '未获取' }}</text>
        </view>
        <view class="info-row">
          <text class="label">初始化状态:</text>
          <text class="value" :class="{ 'success': isInitialized }">
            {{ isInitialized ? '已初始化' : '未初始化' }}
          </text>
        </view>
        <view class="info-row">
          <text class="label">上报状态:</text>
          <text class="value" :class="{ 'success': isReported }">
            {{ isReported ? '已上报' : '未上报' }}
          </text>
        </view>
      </view>
    </view>

    <view class="demo-section">
      <view class="section-title">功能测试</view>
      <view class="button-group">
        <button class="demo-btn" type="primary" @click="reportDevice" :loading="reportLoading">
          上报设备信息
        </button>
      </view>
    </view>
  </view>
</template>

<script setup>
import {ref, onMounted, getCurrentInstance} from 'vue'
import * as pushApi from '@/api/push'

const clientId = ref('')
const isInitialized = ref(false)
const getClientIdLoading = ref(false)
const reportLoading = ref(false)
const isReported = ref(false)

// 在setup期间存储push服务引用
const pushServiceRef = ref(null)

onMounted(() => {

  // 获取推送服务实例
  const instance = getCurrentInstance()
  if (instance?.appContext.config.globalProperties.$push) {
    pushServiceRef.value = instance.appContext.config.globalProperties.$push
    clientId.value = pushServiceRef.value.getClientId() || ''
    isInitialized.value = pushServiceRef.value.isReady()
  }
  
})

// 上报设备信息
async function reportDevice() {
  reportLoading.value = true
  try {

    pushServiceRef.value.reReportClientId()


  } catch (error) {
    console.error('上报失败:', error)
    uni.showToast({
      title: '上报失败',
      icon: 'error'
    })
  } finally {
    reportLoading.value = false
  }
}

function formatTime(timestamp) {
  const date = new Date(timestamp)
  return `${date.getMonth() + 1}/${date.getDate()} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
}

</script>

<style scoped>
.push-demo {
  padding: 40rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.demo-header {
  text-align: center;
  margin-bottom: 40rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.demo-section {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  padding-left: 20rpx;
  border-left: 6rpx solid #007aff;
}

.info-card {
  background-color: #fff;
  border-radius: 10rpx;
  padding: 30rpx;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.info-row:last-child {
  border-bottom: none;
}

.label {
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
}

.value {
  font-size: 28rpx;
  color: #333;
  word-break: break-all;
  max-width: 400rpx;
  text-align: right;
}

.value.success {
  color: #19be6b;
}

.button-group {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.demo-btn {
  border-radius: 10rpx;
  font-size: 30rpx;
}

.message-list {
  background-color: #fff;
  border-radius: 10rpx;
  overflow: hidden;
}

.message-item {
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.message-item:last-child {
  border-bottom: none;
}

.msg-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.msg-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.msg-time {
  font-size: 24rpx;
  color: #999;
}

.msg-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  display: block;
  margin-bottom: 10rpx;
}

.msg-payload {
  margin-top: 10rpx;
  padding: 20rpx;
  background-color: #f8f8f8;
  border-radius: 6rpx;
}

.payload-label {
  font-size: 24rpx;
  color: #999;
  display: block;
  margin-bottom: 10rpx;
}

.payload-value {
  font-size: 24rpx;
  color: #666;
  word-break: break-all;
  font-family: monospace;
}

.empty-message {
  padding: 60rpx;
  text-align: center;
  color: #999;
  font-size: 28rpx;
}

.link-group {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.link-btn {
  background-color: #fff;
  border: 1rpx solid #ddd;
  border-radius: 10rpx;
  font-size: 30rpx;
  color: #007aff;
}
</style>
