import request from '@/utils/request'

// api地址
const api = {
  sendSmsCode: '/app-api/getSmsCode',
  register: '/app-api/register'
}

/**
 * 发送短信验证码
 * @param {string} phone - 手机号
 * @returns {Promise}
 */
export function sendSmsCode(phone) {
  return request.get(api.sendSmsCode, { phone })
}

/**
 * 用户注册
 * @param {Object} data - 注册信息
 * @param {string} data.phone - 手机号
 * @param {string} data.code - 验证码
 * @param {string} data.password - 密码
 * @returns {Promise}
 */
export function register(data) {
  return request.post(api.register, data)
} 