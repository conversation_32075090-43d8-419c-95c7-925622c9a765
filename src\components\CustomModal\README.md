# CustomModal 自定义模态框组件

## 概述

`CustomModal` 是一个自定义的模态框组件，用于替换 uview-plus 的 `up-modal` 组件。它提供了与原组件相似的功能，同时保持了项目的设计风格一致性。

## 功能特性

- ✅ 支持自定义标题
- ✅ 支持自定义按钮文字
- ✅ 支持禁用确认按钮
- ✅ 支持点击遮罩层关闭
- ✅ 支持动画效果
- ✅ 响应式设计
- ✅ 符合项目设计规范

## 基础用法

### 1. 导入组件

```vue
<script setup>
import CustomModal from '@/components/CustomModal/CustomModal.vue'
</script>
```

### 2. 基础模态框

```vue
<template>
  <CustomModal 
    :show="showModal" 
    title="提示"
    @cancel="showModal = false"
    @confirm="handleConfirm"
  >
    <view>这是模态框的内容</view>
  </CustomModal>
</template>

<script setup>
import { ref } from 'vue'

const showModal = ref(false)

const handleConfirm = () => {
  console.log('确认操作')
  showModal.value = false
}
</script>
```

## API 参数

### Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| show | Boolean | false | 是否显示模态框 |
| title | String | '' | 模态框标题 |
| showCancelButton | Boolean | true | 是否显示取消按钮 |
| buttonText | Object | {cancel: '取消', confirm: '确定'} | 按钮文字配置 |
| confirmDisabled | Boolean | false | 是否禁用确认按钮 |
| closeOnClickOverlay | Boolean | true | 是否允许点击遮罩层关闭 |

### Events

| 事件名 | 说明 | 参数 |
|--------|------|------|
| cancel | 点击取消按钮时触发 | - |
| confirm | 点击确认按钮时触发 | - |
| close | 模态框关闭时触发 | - |

## 使用示例

### 1. 确认删除对话框

```vue
<CustomModal 
  :show="showDeleteModal" 
  title="确认删除"
  :buttonText="{cancel: '取消', confirm: '删除'}"
  @cancel="showDeleteModal = false"
  @confirm="handleDelete"
>
  <view style="text-align: center; padding: 20rpx 0;">
    确定要删除这个项目吗？此操作不可撤销。
  </view>
</CustomModal>
```

### 2. 带验证的模态框

```vue
<CustomModal 
  :show="showVerifyModal" 
  title="上传验证"
  :confirmDisabled="!hasImage"
  :buttonText="{cancel: '取消', confirm: '提交验证'}"
  @cancel="showVerifyModal = false"
  @confirm="handleVerify"
>
  <view>
    <view style="margin-bottom: 20rpx;">请上传验证图片：</view>
    <ImageUploader 
      v-model="verifyImages"
      :max-count="1"
      @change="onImageChange"
    />
  </view>
</CustomModal>
```

### 3. 只有确认按钮的模态框

```vue
<CustomModal 
  :show="showInfoModal" 
  title="信息提示"
  :showCancelButton="false"
  @confirm="showInfoModal = false"
>
  <view style="text-align: center; padding: 20rpx 0;">
    操作已完成！
  </view>
</CustomModal>
```

## 迁移指南

### 从 up-modal 迁移

**原代码：**
```vue
<up-modal 
  :show="showModal" 
  :title="title" 
  :showCancelButton="true"
  @cancel="handleCancel" 
  @confirm="handleConfirm"
  :buttonText="{cancel: '取消', confirm: '确定'}"
  :confirmDisabled="disabled"
>
  <view>内容</view>
</up-modal>
```

**新代码：**
```vue
<CustomModal 
  :show="showModal" 
  :title="title" 
  :showCancelButton="true"
  @cancel="handleCancel" 
  @confirm="handleConfirm"
  :buttonText="{cancel: '取消', confirm: '确定'}"
  :confirmDisabled="disabled"
>
  <view>内容</view>
</CustomModal>
```

### 从 uni.showModal 迁移

**原代码：**
```javascript
uni.showModal({
  title: '提示',
  content: '确定要删除吗？',
  confirmText: '删除',
  cancelText: '取消',
  success: (res) => {
    if (res.confirm) {
      handleDelete()
    }
  }
})
```

**新代码：**
```vue
<template>
  <CustomModal 
    :show="showModal" 
    title="提示"
    :buttonText="{cancel: '取消', confirm: '删除'}"
    @cancel="showModal = false"
    @confirm="handleConfirm"
  >
    <view style="text-align: center; padding: 20rpx 0;">
      确定要删除吗？
    </view>
  </CustomModal>
</template>

<script setup>
const showModal = ref(false)

const handleConfirm = () => {
  handleDelete()
  showModal.value = false
}
</script>
```

## 样式定制

组件使用了项目统一的设计规范：

- 主色调：`#1890ff`
- 文字颜色：`#000`（高对比度）
- 边框颜色：`#f0f0f0`
- 圆角：`12rpx`
- 动画：`0.3s ease`

如需自定义样式，可以通过深度选择器覆盖：

```vue
<style>
:deep(.modal-container) {
  border-radius: 16rpx;
}

:deep(.confirm-btn) {
  color: #ff4757;
}
</style>
```

## 注意事项

1. 确保在使用前正确导入组件
2. 使用 `v-model` 或手动控制 `show` 属性来显示/隐藏模态框
3. 记得在事件处理函数中关闭模态框
4. 禁用确认按钮时，点击不会触发 `confirm` 事件
5. 组件支持插槽，可以放置任意内容

## 测试

可以访问测试页面查看组件效果：
```
/pages/test/modal-test.vue
```
