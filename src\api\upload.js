import request from '@/utils/request'

// api地址
const api = {
    uploadFile: '/app-api/upload/file',
}

// 用户登录 (手机号+密码)
export function upload(data) {
    return request.post(api.uploadFile, data)
}

// 上传图片
export function uploadFile(path) {
    console.log('调用上传API，文件路径:', path)

    return request.urlFileUpload(api.uploadFile, {
        files: [{path: path}], // 必填 临时文件路径 格式: [{path: "图片地址"}]
        header: {
            'Content-Type': 'multipart/form-data'  // 修改为正确的文件上传类型
        },
        isFactory: true, //（默认 true 说明：本接口是否调用公共的数据处理方法，设置false后isPrompt参数奖失去作用）
        maxSize: 10 * 1024 * 1024, // 修改为10MB限制，原来的300KB太小了
        onEachUpdate: res => {
            console.log("单张上传成功返回：", res);
        },
        onProgressUpdate: res => {
            console.log("上传进度返回：", res);
        }
    }).catch(error => {
        console.error('上传API调用失败:', error)
        throw error
    })
}
