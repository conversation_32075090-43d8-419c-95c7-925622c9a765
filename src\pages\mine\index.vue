<template>
  <!-- 用户信息 -->
  <userInfo/>
  <view class="container">
    <!-- 功能列表 -->
    <view class="menu-list">
      <view class="menu-item" v-for="(item, index) in menuList" :key="index" @click="handleMenuClick(item)">
        <view class="menu-item-left">
          <image class="menu-icon" :src="item.icon" mode="aspectFit"/>
          <text class="menu-text">{{ item.text }}</text>
        </view>
        <view class="menu-item-right">
          <text v-if="item.value" class="menu-value">{{ item.value }}</text>
          <image v-if="item.path" class="arrow-icon" src="/static/common/arrow-right.png" mode="aspectFit"/>
        </view>
      </view>
    </view>
    <!-- 自定义 TabBar -->
    <CustomTabBar ref="customTabBar"/>
  </view>
</template>

<script setup>
import { onMounted, getCurrentInstance } from 'vue'
import { onShow } from "@dcloudio/uni-app"
import userInfo from "@/components/userInfo/userInfo.vue"
import CustomTabBar from '@/components/CustomTabBar/CustomTabBar.vue'
import tabBarManager from '@/utils/tabBar'

onMounted(() => {
  // 初始化 tabBarManager
  tabBarManager.init()
})

// 页面显示时更新 TabBar 状态
const updateTabBarIndex = () => {
  // 通过 ref 获取 CustomTabBar 组件实例并更新索引
  const tabBarComponent = getCurrentInstance()?.refs?.customTabBar
  if (tabBarComponent && tabBarComponent.setCurrentIndex) {
    tabBarComponent.setCurrentIndex(3) // 我的页面对应索引 3
  }
}

onShow(() => {
  // 更新 TabBar 状态
  updateTabBarIndex()
})

// 菜单列表
const menuList = [
  {
    icon: '/static/user/list.png',
    text: '整改列表',
    path: '/pages/user/publish/index'
  },
  {
    icon: '/static/user/settings.png',
    text: '系统设置',
    path: '/pages/mine/settings'
  },
  {
    icon: '/static/user/about.png',
    text: '联系我们',
    path: '/pages/contact/index'
  },
  // {
  //   icon: '/static/user/about.png',
  //   text: '推送功能演示',
  //   path: '/pages/demo/push-demo'
  // }
]

// 处理菜单点击
const handleMenuClick = (item) => {
  if (item.path) {
    uni.navigateTo({
      url: item.path
    })
  }
}

</script>

<style scoped>
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding: 20rpx 20rpx calc(100rpx + env(safe-area-inset-bottom)) 20rpx; /* 为自定义 TabBar 留出空间，适配苹果安全区域 */
}

.menu-list {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 0 20rpx;
}

.menu-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 0;
  border-bottom: 1px solid #f5f5f5;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item-left {
  display: flex;
  align-items: center;
}

.menu-item-right {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.menu-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
}

.menu-text {
  font-size: 28rpx;
  color: #333;
}

.menu-value {
  font-size: 26rpx;
  color: #999;
}

.arrow-icon {
  width: 32rpx;
  height: 32rpx;
  display: block;
}
</style>
