import { useTabBarStore } from '@/stores/tabBar'
import * as Api from '@/api/user'

/**
 * TabBar 工具类
 * 提供便捷的 API 来管理 tabBar 角标
 */
class TabBarManager {
  constructor() {
    this.store = null
  }

  // 初始化 store（在 setup 中调用）
  init() {
    this.store = useTabBarStore()
    return this
  }

  /**
   * 从服务器获取角标数据并更新本地状态
   */
  async fetchBadgeData() {
    try {
      const result = await  Api.getUserBadgeCount()
      console.log('角标数据:', result)
      if (result.code === 200) {
        const { taskBadge, publishBadge } = result.data
        // 更新本地状态
        this.store.setTaskBadge(taskBadge)
        this.store.setPublishBadge(publishBadge)

        return result.data
      }
    } catch (error) {
      console.error('获取角标数据失败:', error)
      uni.showToast({
        title: '获取角标数据失败',
        icon: 'none'
      })
    }
  }

  /**
   * 设置角标（同时更新服务器和本地状态）
   * @param {string} tabName - tab 名称 ('task', 'publish', 'create', 'mine')
   * @param {number|boolean} count - 角标数量，true 表示显示小红点
   * @param {boolean} updateServer - 是否同步到服务器，默认 true
   */
  async setBadge(tabName, count, updateServer = true) {
    if (!this.store) {
      console.warn('TabBarManager not initialized')
      return
    }

    // 先更新本地状态
    switch (tabName) {
      case 'task':
        this.store.setTaskBadge(count)
        break
      case 'publish':
        this.store.setPublishBadge(count)
        break
      case 'create':
        this.store.setCreateBadge(count)
        break
      case 'mine':
        this.store.setMineBadge(count)
        break
      default:
        console.warn(`Unknown tab name: ${tabName}`)
        return
    }

    // 同步到服务器
    if (updateServer) {
      try {
        await BadgeApi.updateBadgeData(tabName, 'set', count)
      } catch (error) {
        console.error('更新服务器角标失败:', error)
      }
    }
  }

  /**
   * 增加角标数量
   * @param {string} tabName - tab 名称
   * @param {number} count - 增加的数量，默认为 1
   * @param {boolean} updateServer - 是否同步到服务器，默认 true
   */
  async incrementBadge(tabName, count = 1, updateServer = true) {
    if (!this.store) {
      console.warn('TabBarManager not initialized')
      return
    }
    
    this.store.incrementBadge(tabName, count)
    
    if (updateServer) {
      try {
        await BadgeApi.updateBadgeData(tabName, 'increment', count)
      } catch (error) {
        console.error('更新服务器角标失败:', error)
      }
    }
  }

  /**
   * 减少角标数量
   * @param {string} tabName - tab 名称
   * @param {number} count - 减少的数量，默认为 1
   * @param {boolean} updateServer - 是否同步到服务器，默认 true
   */
  async decrementBadge(tabName, count = 1, updateServer = true) {
    if (!this.store) {
      console.warn('TabBarManager not initialized')
      return
    }
    
    this.store.decrementBadge(tabName, count)
    
    if (updateServer) {
      try {
        await BadgeApi.updateBadgeData(tabName, 'decrement', count)
      } catch (error) {
        console.error('更新服务器角标失败:', error)
      }
    }
  }

  /**
   * 清除指定 tab 的角标
   * @param {string} tabName - tab 名称
   * @param {boolean} updateServer - 是否同步到服务器，默认 true
   */
  async clearBadge(tabName, updateServer = true) {
    if (!this.store) {
      console.warn('TabBarManager not initialized')
      return
    }
    
    this.store.clearBadge(tabName)
    
    if (updateServer) {
      try {
        await BadgeApi.updateBadgeData(tabName, 'clear', 0)
      } catch (error) {
        console.error('更新服务器角标失败:', error)
      }
    }
  }

  /**
   * 清除所有角标
   * @param {boolean} updateServer - 是否同步到服务器，默认 true
   */
  async clearAllBadges(updateServer = true) {
    if (!this.store) {
      console.warn('TabBarManager not initialized')
      return
    }
    
    this.store.clearAllBadges()
    
    if (updateServer) {
      try {
        await BadgeApi.clearAllBadges()
      } catch (error) {
        console.error('清除服务器角标失败:', error)
      }
    }
  }

  /**
   * 获取指定 tab 的角标数量
   * @param {string} tabName - tab 名称
   * @returns {number} 角标数量
   */
  getBadge(tabName) {
    if (!this.store) {
      console.warn('TabBarManager not initialized')
      return 0
    }

    switch (tabName) {
      case 'task':
        return this.store.taskBadge
      case 'publish':
        return this.store.publishBadge
      case 'create':
        return this.store.createBadge
      case 'mine':
        return this.store.mineBadge
      default:
        console.warn(`Unknown tab name: ${tabName}`)
        return 0
    }
  }

  /**
   * 获取总角标数量
   * @returns {number} 总角标数量
   */
  getTotalBadgeCount() {
    if (!this.store) {
      console.warn('TabBarManager not initialized')
      return 0
    }
    return this.store.getTotalBadgeCount()
  }

  /**
   * 显示小红点（不显示数字）
   * @param {string} tabName - tab 名称
   * @param {boolean} updateServer - 是否同步到服务器，默认 true
   */
  async showDot(tabName, updateServer = true) {
    await this.setBadge(tabName, true, updateServer)
  }

  /**
   * 隐藏角标
   * @param {string} tabName - tab 名称
   * @param {boolean} updateServer - 是否同步到服务器，默认 true
   */
  async hideBadge(tabName, updateServer = true) {
    await this.setBadge(tabName, 0, updateServer)
  }

  /**
   * 刷新角标数据（从服务器重新获取）
   */
  async refreshBadgeData() {
    return await this.fetchBadgeData()
  }

  /**
   * 获取任务统计数据并更新相应角标
   */
  async updateTaskStatistics() {
    try {
      const result = await BadgeApi.getTaskStatistics()
      if (result.code === 200) {
        const { pendingTasks, publishTasks, unreadMessages } = result.data
        
        // 根据统计数据更新角标（不同步到服务器，避免重复请求）
        await this.setBadge('task', pendingTasks, false)
        await this.setBadge('publish', publishTasks, false)
        await this.setBadge('mine', unreadMessages, false)
        
        console.log('任务统计数据更新成功:', result.data)
        return result.data
      }
    } catch (error) {
      console.error('获取任务统计数据失败:', error)
    }
  }
}

// 创建单例实例
const tabBarManager = new TabBarManager()

// 导出实例和类
export { tabBarManager, TabBarManager }
export default tabBarManager 