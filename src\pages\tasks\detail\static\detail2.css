page {
    width: 100%;
    height: 100%;
    overflow: auto;
}

page::-webkit-scrollbar {
    display: none;
}

.container {
    width: 740rpx;
    margin: 0 auto;
    padding: 20rpx 10rpx 0;
    background: #ffffff;
    border-radius: 10rpx;
    position: relative;
    box-sizing: border-box;
}

.tasks-info {
    width: 100%;
    position: relative;
    font-size: 16px;
}

.tasks-info-top {
    width: 100%;
    color: #585858;
    display: flex;
    align-content: center;
}

.tasks-info-top .task-title {
    width: 240rpx;
    height: 50rpx;
    line-height: 50rpx;
}

.tasks-info-top .task-title .task-date {
    font-size: 12px;
    color: #999;
    padding-left: 10rpx;
}

.tasks-info-top .tasks-no {
    flex: 1;
    height: 50rpx;
    line-height: 50rpx;
}

.task-desc {
    height: 50rpx;
    line-height: 50rpx;
    margin-top: 10rpx;
}

.location-list {
    margin-top: 40rpx;
}

.tasks-type {
    position: absolute;
    font-size: 12px;
    color: #9cb9e0;
    font-weight: bold;
    right: 0;
    top: 20rpx;
    padding: 8px 12px;
    background-color: #e5f2fa;
    border-radius: 0 0 0 10px;
}

.points-title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 10rpx;
}

.points-index-tag {
    width: 40rpx;
    height: 40rpx;
    line-height: 40rpx;
    float: left;
    font-size: 18rpx;
    background: #7db342;
    border-radius: 100%;
    text-align: center;
    color: white;
    box-sizing: border-box;
    margin: auto 10rpx auto auto;
}

.points-choices {
    display: flex;
    gap: 12px;
}

.btn-group {
    padding-top: 20rpx;
    padding-bottom: 80rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.btn-finish, .btn-publish {
    width: 600rpx;
    height: 80rpx;
    margin: 20rpx 0;
    border-radius: 60rpx;
}

/* 质控点列表样式 */
.points-list-container {
    margin: 20rpx 0;
}

.points-title {
    font-size: 28rpx;
    color: #666;
    margin-bottom: 20rpx;
    padding: 0 20rpx;
}

.points-list {
    display: flex;
    flex-direction: column;
    gap: 16rpx;
    padding: 0 20rpx;
}

.point-item {
    position: relative;
    display: flex;
    align-items: center;
    padding: 24rpx 20rpx;
    background-color: #fff;
    border-radius: 12rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.point-status-bg {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 8rpx;
}

.status-notdone .point-status-bg {
    background-color: #909399; /* 灰色 - 未检查/待检查 */
}

.status-done .point-status-bg {
    background-color: #19be6b; /* 绿色 - 已完成且完全符合 */
}

.status-error .point-status-bg {
    background-color: #ff9f43; /* 橙色 - 已完成但有问题需要整改 */
}

.status-pending .point-status-bg {
    background-color: #e74c3c; /* 红色 - 待整改 */
}

.point-index {
    width: 50rpx;
    height: 50rpx;
    border-radius: 50%;
    background-color: #f2f2f2;
    color: #666;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24rpx;
    margin-right: 20rpx;
    flex-shrink: 0;
}

.status-done .point-index {
    background-color: rgba(25, 190, 107, 0.1);
    color: #19be6b;
}

.status-error .point-index {
    background-color: rgba(255, 159, 67, 0.1);
    color: #ff9f43;
}

.status-pending .point-index {
    background-color: rgba(231, 76, 60, 0.3);
    color: #e74c3c;
}

.point-name {
    flex: 1;
    font-size: 28rpx;
    line-height: 1.4;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    word-break: break-all;
}

.point-status {
    margin-left: 20rpx;
    flex-shrink: 0;
}

.status-tag {
    font-size: 24rpx;
    padding: 4rpx 12rpx;
    border-radius: 20rpx;
    font-weight: 500;
}

.status-notdone {
    background-color: #f2f2f2;
    color: #909399;
}

.status-done {
    background-color: rgba(25, 190, 107, 0.1);
    color: #19be6b;
}

.status-error {
    background-color: rgba(255, 159, 67, 0.1);
    color: #ff9f43;
}

.status-pending {
    background-color: rgba(231, 76, 60, 0.3);
    color: #e74c3c;
}

.status-watermark {
    position: absolute;
    right: 10rpx;
    bottom: 10rpx;
    font-size: 40rpx;
    color: rgba(25, 190, 107, 0.1);
    font-weight: bold;
    transform: rotate(-15deg);
    opacity: 0.7;
}

.status-watermark.error {
    color: rgba(231, 76, 60, 0.1);
}

/* 抽屉样式 */
.drawer-container {
    display: flex;
    flex-direction: column;
    height: 80vh;
    background-color: #f8f8f8;
    overflow-y: auto;
}

.drawer-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx 30rpx 20rpx;
}

.drawer-title {
    font-size: 28rpx;
    color: #666;
}

.completion-rate {
    font-size: 24rpx;
    color: #909399;
}

.drawer-content {
    flex: 1;
    width: 100%;
    padding: 0 30rpx;
    box-sizing: border-box;
}

.drawer-footer {
    padding: 20rpx 30rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;
}

.footer-btn {
    padding: 12rpx 24rpx;
    border-radius: 40rpx;
    font-size: 28rpx;
}

.cancel-btn {
    background-color: #f2f2f2;
    color: #909399;
}

.confirm-btn {
    background-color: #19be6b;
    color: #fff;
}

.points-choices {
    margin-bottom: 20rpx;
}

.number-inputs {
    margin-bottom: 20rpx;
}

.problem_desc-wap {
    padding-bottom: 20rpx;
    margin-bottom: 20rpx;
}

.problem_desc-input {
    width: 100%;
    padding: 16rpx;
    background-color: #fff;
    border: 1rpx solid #909399;
    border-radius: 40rpx;
    box-sizing: border-box;
    outline: none;
}

.upload-image {
    margin-bottom: 20rpx;
}

.correction-date {
    margin-top: 20rpx;
    display: flex;
    align-items: center;
}

.date-label {
    margin-right: 16rpx;
    color: #909399;
}

.date-picker {
    display: flex;
    align-items: center;
    padding: 12rpx 16rpx;
    border: 1rpx solid #909399;
    border-radius: 40rpx;
    cursor: pointer;
}

/* 执行人选择样式 */
.executor-select {
  margin: 20rpx 0;
}

.executor-search {
  margin-bottom: 10rpx;
}

.picker-value {
  height: 88rpx;
  line-height: 88rpx;
  padding: 0 30rpx;
  background-color: #ffffff;
  border: 2rpx solid #e5e5e5;
  border-radius: 12rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 28rpx;
  color: #333;
}

.picker-value:active {
  background-color: #f8f8f8;
}

.picker-arrow {
  color: #999;
  font-size: 24rpx;
  margin-left: 10rpx;
}
