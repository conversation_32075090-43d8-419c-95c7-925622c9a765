/**
 * UniPush 2.0 推送服务
 */
import * as pushApi from '@/api/push'
import { useUserStore } from '@/stores/user'

class PushService {
    constructor() {
        this.clientId = null
        this.isInitialized = false
    }

    /**
     * 初始化推送服务 - 获取 clientId
     */
    async init() {
        return new Promise((resolve, reject) => {
            // #ifdef APP-PLUS
            const push = plus.push

            // 获取客户端标识 - 使用正确的方法
            uni.getPushClientId({
                success: (res) => {
                    this.clientId = res.cid
                    this.isInitialized = true

                    console.log('🎉 Push初始化成功')
                    console.log('📱 ClientID:', this.clientId)

                    // 如果用户登陆了，立即上报 clientId 到服务端
                    const userStore = useUserStore()
                    if (userStore.isLogin) {
                        this.reportClientId()
                    }
                    resolve({clientid: this.clientId, platform: 'app'})
                },
                fail: (error) => {
                    console.error('❌ Push初始化失败:', error)
                    this.clientId = JSON.stringify(error)
                    reject(error)
                }
            })
			// #endif
			reject(false)
		})
    }

    /**
     * 上报 clientId 到服务端
     */
    async reportClientId() {
        if (!this.clientId) {
            console.warn('⚠️ ClientID 为空，无法上报')
            return
        }

        try { 
            const deviceInfo = {
                clientId: this.clientId,
                platform: uni.getSystemInfoSync().platform,
                appVersion: this.getAppVersion(),
                osVersion: uni.getSystemInfoSync().system,
                deviceModel: uni.getSystemInfoSync().model,
                reportTime: new Date().toISOString()
            }

            console.log('📤 上报设备信息:', deviceInfo)

            const response = await pushApi.registerDevice(deviceInfo)

            if (response.statusCode === 200 && response.data.code === 200) {
                console.log('✅ ClientID 上报成功')
                // 存储上报状态
                uni.setStorageSync('clientIdReported', true)
                uni.setStorageSync('clientIdReportTime', Date.now())
            }
        } catch (error) {
            console.error('❌ ClientID 上报失败:', error)
        }
    }


    /**
     * 获取应用版本
     */
    getAppVersion() {
        // #ifdef APP-PLUS
        return plus.runtime.version || '1.0.0'
        // #endif 
    }

    /**
     * 获取用户token
     */
    getToken() {
        try {
            return uni.getStorageSync('token') || ''
        } catch {
            return ''
        }
    }

    /**
     * 获取 ClientID
     */
    getClientId() {
        return this.clientId
    }

    /**
     * 检查是否已初始化
     */
    isReady() {
        return this.isInitialized && this.clientId
    }

    /**
     * 重新上报 ClientID（用于用户登录后）
     */
    async reReportClientId() {
        if (this.isReady()) {
            await this.reportClientId()
        }
    }

    /**
     * 手动获取ClientID（用于调试）
     */
    async refreshClientId() {
        return new Promise((resolve, reject) => {
            uni.getPushClientId({
                success: (res) => {
                    this.clientId = res.cid
                    console.log('🔄 ClientID已更新:', this.clientId)
                    resolve(res.cid)
                },
                fail: (error) => {
                    console.error('❌ 获取ClientID失败:', error)
                    this.clientId = JSON.stringify(error)
                    reject(error)
                }
            })
        })
    }
    
}

export default new PushService()
