# CustomPopup 自定义弹出层组件

## 概述

`CustomPopup` 是一个自定义的弹出层组件，用于替换 uview-plus 的 `up-popup` 组件。它支持多种弹出模式，提供了丰富的配置选项和流畅的动画效果。

## 功能特性

- ✅ 支持多种弹出模式（center、bottom、top、left、right）
- ✅ 支持自定义圆角和尺寸
- ✅ 支持滚动内容
- ✅ 支持拖拽条（底部弹出时）
- ✅ 支持点击遮罩层关闭
- ✅ 支持关闭按钮
- ✅ 流畅的动画效果
- ✅ 响应式设计
- ✅ 符合项目设计规范

## 基础用法

### 1. 导入组件

```vue
<script setup>
import CustomPopup from '@/components/CustomPopup/CustomPopup.vue'
</script>
```

### 2. 底部弹出（最常用）

```vue
<template>
  <CustomPopup 
    :show="showPopup" 
    mode="bottom"
    :scrollable="true"
    @close="showPopup = false"
  >
    <view class="popup-content">
      <view class="popup-header">
        <text class="popup-title">选择操作</text>
      </view>
      <view class="popup-body">
        <!-- 内容区域 -->
      </view>
    </view>
  </CustomPopup>
</template>

<script setup>
import { ref } from 'vue'

const showPopup = ref(false)
</script>
```

## API 参数

### Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| show | Boolean | false | 是否显示弹出层 |
| mode | String | 'center' | 弹出模式：center/bottom/top/left/right |
| round | Boolean/String/Number | false | 圆角设置，true为默认圆角，可设置具体数值 |
| zIndex | String/Number | 999 | 层级 |
| closeable | Boolean | false | 是否显示关闭按钮 |
| closeOnClickOverlay | Boolean | true | 是否允许点击遮罩层关闭 |
| scrollable | Boolean | false | 内容是否可滚动 |
| showDragBar | Boolean | true | 是否显示拖拽条（仅底部弹出时有效） |
| width | String | '' | 自定义宽度 |
| height | String | '' | 自定义高度 |
| maxHeight | String | '80vh' | 最大高度（滚动模式下有效） |

### Events

| 事件名 | 说明 | 参数 |
|--------|------|------|
| close | 弹出层关闭时触发 | - |
| open | 弹出层打开时触发 | - |

## 使用示例

### 1. 居中弹出

```vue
<CustomPopup 
  :show="showCenter" 
  mode="center"
  :round="true"
  width="600rpx"
  @close="showCenter = false"
>
  <view style="padding: 40rpx;">
    <text>居中弹出的内容</text>
  </view>
</CustomPopup>
```

### 2. 底部抽屉（表单）

```vue
<CustomPopup 
  :show="showDrawer" 
  mode="bottom"
  :round="32"
  :scrollable="true"
  :close-on-click-overlay="false"
  @close="closeDrawer"
>
  <view class="drawer-container">
    <view class="drawer-header">
      <text class="drawer-title">填写信息</text>
    </view>
    <view class="drawer-content">
      <!-- 表单内容 -->
      <form>
        <!-- 表单项 -->
      </form>
    </view>
    <view class="drawer-footer">
      <button @click="closeDrawer">取消</button>
      <button @click="submitForm">确认</button>
    </view>
  </view>
</CustomPopup>
```

### 3. 侧边栏

```vue
<CustomPopup 
  :show="showSidebar" 
  mode="left"
  width="500rpx"
  @close="showSidebar = false"
>
  <view class="sidebar-content">
    <view class="sidebar-header">
      <text>菜单</text>
    </view>
    <view class="sidebar-menu">
      <!-- 菜单项 -->
    </view>
  </view>
</CustomPopup>
```

### 4. 可滚动内容

```vue
<CustomPopup 
  :show="showScrollable" 
  mode="bottom"
  :scrollable="true"
  :max-height="60vh"
  @close="showScrollable = false"
>
  <view style="padding: 20rpx;">
    <view v-for="item in longList" :key="item.id" class="list-item">
      {{ item.name }}
    </view>
  </view>
</CustomPopup>
```

## 迁移指南

### 从 up-popup 迁移

**原代码：**
```vue
<up-popup
  :show="showPopup"
  @close="closePopup"
  mode="bottom"
  round="16"
  z-index="99"
  :closeable="false"
  :close-on-click-overlay="false"
  :scrollable="true"
>
  <view>内容</view>
</up-popup>
```

**新代码：**
```vue
<CustomPopup
  :show="showPopup"
  @close="closePopup"
  mode="bottom"
  :round="32"
  :z-index="99"
  :closeable="false"
  :close-on-click-overlay="false"
  :scrollable="true"
>
  <view>内容</view>
</CustomPopup>
```

### 主要变化

1. **圆角属性**：`round="16"` → `:round="32"`（数值需要用 v-bind）
2. **层级属性**：`z-index="99"` → `:z-index="99"`（数值需要用 v-bind）
3. **其他属性**：基本保持一致

## 样式定制

组件使用了项目统一的设计规范：

- 遮罩层：`rgba(0, 0, 0, 0.5)`
- 背景色：`white`
- 圆角：`12rpx`（默认）、`24rpx`（round模式）
- 动画：`0.3s ease-out`
- 拖拽条：`#d9d9d9`

### 自定义样式

```vue
<style>
:deep(.popup-container) {
  background: #f5f5f5;
}

:deep(.drag-handle) {
  background: #1890ff;
}

:deep(.close-btn) {
  background: rgba(255, 0, 0, 0.1);
}
</style>
```

## 注意事项

1. 确保在使用前正确导入组件
2. 使用 `v-model` 或手动控制 `show` 属性来显示/隐藏弹出层
3. 记得在事件处理函数中关闭弹出层
4. 滚动模式下建议设置 `max-height` 限制高度
5. 底部弹出时会自动显示拖拽条，可通过 `show-drag-bar` 控制
6. 组件支持插槽，可以放置任意内容

## 动画效果

- **居中弹出**：淡入 + 缩放效果
- **底部弹出**：从下往上滑入
- **顶部弹出**：从上往下滑入
- **左侧弹出**：从左往右滑入
- **右侧弹出**：从右往左滑入

所有动画都使用 `0.3s ease-out` 的过渡效果，确保流畅的用户体验。
