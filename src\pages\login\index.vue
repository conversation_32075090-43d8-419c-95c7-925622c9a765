<template>
  <view class="t-login">
    <!-- 页面装饰图片 -->
    <image class="img-a" src="@/static/login-head.png"></image>
    <image class="img-b" src="@/static/3.png"></image>
    <!-- 标题 -->
    <view class="t-b">{{ title }}</view>
    <form class="cl">
      <view class="t-a">
        <image src="@/static/sj.png"></image>
        <input type="text" name="username" placeholder="请输入用户名" v-model="username"/>
      </view>
      <view class="t-a">
        <image src="@/static/pwd.png"></image>
        <input type="password" name="password" placeholder="请输入密码" v-model="password"/>
      </view>
      <view class="t-a remember-pwd">
        <label class="checkbox">
          <checkbox-group @change="rememberHandler">
            <checkbox :checked="rememberPassword" value="true"/>
          </checkbox-group>
          记住密码
        </label>
      </view>
      <button @click="login()" :disabled="!agreedPrivacy" :class="{'button-disabled': !agreedPrivacy}">登 录</button>

      <!-- 隐私协议选择框 -->
      <view class="privacy-agreement">
        <label class="checkbox">
          <checkbox-group @change="privacyHandler">
            <checkbox :checked="agreedPrivacy" value="true"/>
          </checkbox-group>
          <text>已阅读并同意</text>
          <text class="privacy-link" @click="showPrivacyPolicy">《隐私政策》</text>
        </label>
      </view>

      <!-- 底部区域 -->
      <view class="bottom-area">
        <view v-if="isRegister" class="register-link" @click="goToRegister">没有账号？去注册</view>
      </view>
    </form>
  </view>
</template>

<script setup>
import {ref, onMounted, getCurrentInstance} from 'vue';
import {useUserStore} from '@/stores/user'
import * as HomeApi from '@/api/home/<USER>'

const store = useUserStore()

const title = ref('欢迎回来！')
const username = ref('')
const password = ref('')
// 按钮禁用
let disabled = false

// 添加记住密码状态
const rememberPassword = ref(false)
// 添加隐私协议同意状态
const agreedPrivacy = ref(false)
// 网络权限状态
const hasNetworkPermission = ref(false)
// 添加全局标志，控制网络弹窗显示
const networkDialogShowing = ref(false)

// 在setup期间存储push服务引用
const pushServiceRef = ref(null)

// 添加页面加载时读取存储
onMounted(() => {
  // 获取推送服务实例
  const instance = getCurrentInstance()
  if (instance?.appContext.config.globalProperties.$push) {
    pushServiceRef.value = instance.appContext.config.globalProperties.$push
  }

  // 从本地存储读取记住的账号和密码
  const savedAccount = uni.getStorageSync('savedAccount')
  const savedPassword = uni.getStorageSync('savedPassword')
  if (savedAccount && savedPassword) {
    username.value = savedAccount
    password.value = savedPassword
    rememberPassword.value = true
  }

  // 初始化时检查网络权限和状态
  checkNetworkPermission(3)
  // 监听网络状态变化
  watchNetworkChange()
})

const rememberHandler = (event) => {
  rememberPassword.value = event.detail.value[0] === 'true'
}

// 处理隐私协议选择
const privacyHandler = (event) => {
  agreedPrivacy.value = event.detail.value[0] === 'true'
}

// 显示隐私协议
const showPrivacyPolicy = () => {
  uni.navigateTo({
    url: '/pages/contact/privacy'
  })
}

// 获取配置
const isRegister = ref(false)
const getHomeSetting = () => {
  // 确保有网络权限后再调用API
  if (!hasNetworkPermission.value) {
    console.log('等待网络权限...')
    return
  }

  HomeApi.setting().then((res) => {
    // console.log(res.data.value)
    isRegister.value = res.data.value === '1'
  }).catch(err => {
    console.error('获取配置失败:', err)
  })
}

// 检查网络权限和状态
const checkNetworkPermission = (flag) => {
  console.log('检查网络权限和状态>>>>>' + flag)
  uni.getNetworkType({
    success: (res) => {
      console.log('当前网络类型:', res.networkType)
      if (res.networkType === 'none') {
        hasNetworkPermission.value = false
      } else {
        hasNetworkPermission.value = true
        console.log('网络连接正常，类型:', res.networkType)
        // 网络连接正常后，获取首页设置
        getHomeSetting()
      }
    },
    fail: (err) => {
      console.error('获取网络状态失败:', err)
      hasNetworkPermission.value = false
      if (!networkDialogShowing.value) {
        uni.showToast({
          title: '无法获取网络状态',
          icon: 'none',
          duration: 2000
        })
      }
    },
    complete: () => {
      console.log('网络状态检查完成，当前状态:', hasNetworkPermission.value ? '已连接' : '未连接')
    }
  })
}

// 弹出网络设置对话框
const showNetworkSettingDialog = (tag) => {

  console.log("showNetworkSettingDialog======tag>>>>>", tag)

  // 避免重复提示
  if (hasNetworkPermission.value) {
    console.log('网络已连接，不需要显示设置对话框')
    return
  }

  // 防止重复显示弹窗
  if (networkDialogShowing.value) {
    console.log('网络设置对话框已显示，避免重复')
    return
  }

  console.log('网络连接失败 >>>>>>  1')
  networkDialogShowing.value = true
  uni.showModal({
    title: '网络连接失败',
    content: '请检查您的网络连接后重试',
    confirmText: '去设置',
    cancelText: '取消',
    success: (res) => {
      networkDialogShowing.value = false
      console.log('网络设置对话框结果:', res)
      if (res.confirm) {
        console.log('跳转网络设置')
        // 尝试跳转到设置页面
        goToNetworkSettings()
      }
    }
  })
}

// 跳转到网络设置
const goToNetworkSettings = () => {
  console.log('尝试打开网络设置...')

  // #ifdef APP-PLUS
  try {
    uni.openAppAuthorizeSetting({
      success(res) {
        console.log('打开应用授权设置成功:', res)
        // 设置完成后延迟检查，增加重试机制
        let retryCount = 0
        const maxRetries = 3
        const checkNetwork = () => {
          setTimeout(() => {
            console.log(`第${retryCount + 1}次检查网络状态`)
            uni.getNetworkType({
              success: (res) => {
                if (res.networkType !== 'none') {
                  console.log('网络已连接，类型:', res.networkType)
                  hasNetworkPermission.value = true
                  getHomeSetting()
                } else if (retryCount < maxRetries) {
                  retryCount++
                  checkNetwork()
                } else {
                  console.log('达到最大重试次数，网络仍未连接')
                  hasNetworkPermission.value = false
                  showNetworkSettingDialog(1)
                }
              },
              fail: () => {
                if (retryCount < maxRetries) {
                  retryCount++
                  checkNetwork()
                } else {
                  hasNetworkPermission.value = false
                  showNetworkSettingDialog(2)
                }
              }
            })
          }, 2000) // 每次检查间隔2秒
        }
        checkNetwork()
      },
      fail(err) {
        console.error('打开应用授权设置失败:', err)
        fallbackToSystemSettings()
      }
    })
  } catch (e) {
    console.error('打开应用授权设置异常:', e)
    fallbackToSystemSettings()
  }
  // #endif

  // #ifdef H5 || MP
  // 非APP环境提示用户
  uni.showModal({
    title: '设置网络',
    content: '请手动打开系统设置，检查网络连接',
    showCancel: false,
    confirmText: '知道了',
    success: () => {
      // 检查网络是否已连接
      setTimeout(() => {
        console.log('提示后延迟检查网络状态')
        checkNetworkPermission(1)
      }, 3000)
    }
  })
  // #endif
}

// 打开系统设置的回退方案
const fallbackToSystemSettings = () => {
  console.log('尝试使用回退方案打开系统设置')
  // #ifdef APP-PLUS
  try {
    // 尝试使用plus接口打开系统设置
    if (plus && plus.runtime) {
      // Android设备
      if (plus.os.name.toLowerCase() === 'android') {
        plus.runtime.openURL('package:com.android.settings')
      }
      // iOS设备
      else if (plus.os.name.toLowerCase() === 'ios') {
        plus.runtime.openURL('app-settings:')
      } else {
        uni.showModal({
          title: '提示',
          content: '请手动打开系统设置并允许网络权限',
          showCancel: false
        })
      }
    } else {
      uni.showModal({
        title: '提示',
        content: '请手动打开系统设置并允许网络权限',
        showCancel: false
      })
    }
  } catch (e) {
    console.error('打开系统设置异常:', e)
    uni.showModal({
      title: '提示',
      content: '请手动打开系统设置并允许网络权限',
      showCancel: false
    })
  }
  // #endif

  // #ifdef H5 || MP
  uni.showModal({
    title: '提示',
    content: '请手动打开系统设置并允许网络权限',
    showCancel: false
  })
  // #endif

  // 设置后延迟检查网络状态
  setTimeout(() => {
    console.log('回退方案设置后延迟检查网络状态')
    checkNetworkPermission(2)
  }, 3000)
}

// 监听网络状态变化
const watchNetworkChange = () => {
  // 添加网络状态检查防抖标志
  let isCheckingNetwork = false

  uni.onNetworkStatusChange((res) => {
    console.log('网络状态变化:', res.isConnected, res.networkType)

    // 防止重复处理
    if (isCheckingNetwork) {
      console.log('正在处理网络状态变化，忽略此次回调')
      return
    }

    isCheckingNetwork = true

    // 网络已连接时，立即隐藏可能显示的弹窗
    if (res.isConnected && networkDialogShowing.value) {
      console.log('网络已连接，尝试隐藏弹窗')
      uni.hideLoading()
      uni.hideToast()
      // 尝试关闭模态框
      try {
        uni.hideModal()
      } catch (e) {
        console.error('尝试关闭模态框失败:', e)
      }
      networkDialogShowing.value = false
    }

    // 延迟处理网络状态变化，确保系统状态已稳定
    setTimeout(() => {
      // 更新状态前检查是否真的有变化
      if (hasNetworkPermission.value !== res.isConnected) {
        hasNetworkPermission.value = res.isConnected

        if (res.isConnected) {
          console.log('网络已恢复连接')
          // 网络已连接，调用API
          getHomeSetting()
          // 清除可能存在的网络提示
          uni.hideToast()
        } else {
          console.log('网络已断开')
          // 网络断开，显示网络设置对话框
          showNetworkSettingDialog(3)
        }
      } else {
        console.log('网络状态未实际变化，当前状态:', hasNetworkPermission.value ? '已连接' : '未连接')
      }

      isCheckingNetwork = false
    }, 1000)
  })
}

const login = () => {
  // 再次检查网络状态，以避免状态不同步
  uni.getNetworkType({
    success: (res) => {
      if (res.networkType !== 'none') {
        proceedWithLogin()
      } else {
        showNetworkSettingDialog(4)
      }
    },
    fail: () => {
      showNetworkSettingDialog(5)
    }
  })
}

// 实际的登录处理逻辑
const proceedWithLogin = () => {
  if (!agreedPrivacy.value) {
    uni.showToast({title: '请先阅读并同意隐私协议', icon: 'none'})
    return
  }

  if (!username.value) {
    uni.showToast({title: '请输入用户名', icon: 'none'})
    return
  }
  if (!password.value) {
    uni.showToast({title: '请输入密码', icon: 'none'})
    return
  }
  if (disabled) {
    return
  }

  // 再次确认网络状态
  uni.getNetworkType({
    success: (res) => {
      if (res.networkType !== 'none') {
        // 更新网络状态
        hasNetworkPermission.value = true

        uni.showLoading({
          title: '请稍等',
          mask: false
        })
        disabled = true

        // 登陆
        store.login(username.value, password.value).then(data => {
          // 登录成功后处理记住密码
          if (rememberPassword.value) {
            uni.setStorageSync('savedAccount', username.value)
            uni.setStorageSync('savedPassword', password.value)
          } else {
            uni.removeStorageSync('savedAccount')
            uni.removeStorageSync('savedPassword')
          }

          // #ifdef APP-PLUS
          // 上报ClientID
          pushServiceRef.value && pushServiceRef.value.reReportClientId()
          // #endif

          uni.showToast({title: '登录成功！', icon: 'none'})
          // 跳转主页
          loginSuccess()
        }).catch(err => {
          disabled = false
        }).finally(() => {
          uni.hideLoading()
        })
      } else {
        hasNetworkPermission.value = false
        showNetworkSettingDialog(6)
      }
    },
    fail: () => {
      hasNetworkPermission.value = false
      showNetworkSettingDialog(7)
    }
  })
}

// 登录成功后的处理
const loginSuccess = () => {
  // 延迟执行跳转，确保状态已更新
  setTimeout(() => {
    uni.redirectTo({
      url: '/pages/tasks/list/list'
    })
  }, 1500)
}

// 跳转到注册页面
const goToRegister = () => {
  uni.navigateTo({
    url: '/pages/register/index'
  })
}


</script>
<style>
uni-page-body {
  width: 100%;
  height: 100vh;
  overflow: hidden;
  position: relative;
}

.t-login {
  width: 600rpx;
  height: 100vh;
  margin: 0 auto;
  font-size: 28rpx;
  color: #000;
  position: relative;
}

.img-a {
  position: relative;
  width: 100%;
  height: 130rpx;
  top: 140rpx;
  right: 0;
  margin-bottom: 200rpx;
}

.img-b {
  position: absolute;
  width: 50%;
  left: -150rpx;
  bottom: -150rpx;
}

.t-login button {
  font-size: 28rpx;
  background: #5677fc;
  color: #fff;
  height: 90rpx;
  line-height: 90rpx;
  border-radius: 50rpx;
  box-shadow: 0 5px 7px 0 rgba(86, 119, 252, 0.2);
}

.t-login .button-disabled {
  background: #cccccc;
  box-shadow: 0 5px 7px 0 rgba(204, 204, 204, 0.2);
}

.t-login input {
  padding: 0 20rpx 0 120rpx;
  height: 90rpx;
  line-height: 90rpx;
  margin-bottom: 50rpx;
  background: #f8f7fc;
  border: 1px solid #e9e9e9;
  font-size: 28rpx;
  border-radius: 50rpx;
}

.t-login .t-a {
  position: relative;
}

.t-login .t-a image {
  width: 40rpx;
  height: 40rpx;
  position: absolute;
  left: 40rpx;
  top: 28rpx;
  border-right: 2rpx solid #dedede;
  padding-right: 20rpx;
}

.t-login .t-b {
  text-align: left;
  font-size: 46rpx;
  color: #000;
  padding: 50rpx 0 120rpx 0;
  font-weight: bold;
}

.t-login .t-e image {
  width: 50rpx;
  height: 50rpx;
}

.t-login .t-f {
  text-align: center;
  margin: 200rpx 0 0 0;
  color: #666;
}

.t-login .t-f text {
  margin-left: 20rpx;
  color: #aaaaaa;
  font-size: 27rpx;
}

.cl {
  zoom: 1;
}

.cl:after {
  clear: both;
  display: block;
  visibility: hidden;
  height: 0;
  content: '\20';
}

/* 添加复选框样式 */
.remember-pwd {
  margin: 30rpx 0;
  display: flex;
  align-items: center;
}

.checkbox {
  display: flex;
  align-items: center;
  font-size: 28rpx;
}

/* 隐私协议样式 */
.privacy-agreement {
  margin: 40rpx 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.privacy-link {
  color: #5677fc;
  margin-left: 5rpx;
}

/* 底部区域样式 */
.bottom-area {
  position: fixed;
  bottom: 60rpx;
  left: 0;
  right: 0;
  text-align: center;
}

/* 添加注册链接样式 */
.register-link {
  color: #5677fc;
  font-size: 28rpx;
  padding: 20rpx;
}
</style>
