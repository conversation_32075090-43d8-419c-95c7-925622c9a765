<template>
  <!-- 用户信息 -->
  <userInfo/>

  <view class="container">
    <!-- Tab 导航部分 -->
    <view class="tabs">
      <view class="tab" :class="{ active: activeTab === index }" v-for="(tab, index) in tabList" :key="index"
            @click="selectTab(index)">
        {{ tab }}
        <view class="underline" v-if="activeTab === index"></view>
      </view>
    </view>

    <!-- 任务卡片 -->
    <view class="task-list">
      <!-- 多个任务卡片 -->
      <task-card v-for="(task, index) in tasks" :key="index" :title="task.title" :date="task.date"
                 :description="task.description" :locations="task.locations" :timeRange="task.timeRange"
                 @accept="handleAccept(task)" @click="goDetail" task-info=""></task-card>
    </view>
  </view>
</template>

<script setup>
import {ref} from 'vue'
import TaskCard from '@/components/taskCard/TaskCard.vue'
import userInfo from "@/components/userInfo/userInfo.vue"

const locations = ref(['2-1病区', '2-2病区', '2-3病区', '2-4病区']);


// Tab 列表及选中状态
const tabList = ['所有', '待接受', '待执行', '进行中', '已完成'];
const activeTab = ref(0);

// 切换 Tab 方法
const selectTab = (index) => {
  activeTab.value = index;
}
// 任务列表
const tasks = [{
  title: '自动任务',
  date: '2天前',
  description: '病区例行巡查-这是任务标题，不换行，点显全点显全点显全点显全',
  locations: ['2-1病区', '2-2病区', '2-3病区', '2-4病区', '2-4病区', '2-4病区'],
  timeRange: '2024-9-23 至 2024-9-30',
},
  {
    title: '手动任务',
    date: '3天前',
    description: '病区专项巡查-第二任务标题',
    locations: ['1-1病区', '1-2病区'],
    timeRange: '2024-9-15 至 2024-9-20',
  },
];

// 接受任务的处理逻辑
const handleAccept = (task) => {
  console.log('接受任务：', task.title);
};

const goDetail = () => {
  uni.navigateTo({
    url: "/pages/tasks/detail/detail"
  })
}
</script>

<style scoped>
.container {
  background-color: #f5f5f5;
}

.tabs {
  display: flex;
  justify-content: space-around;
  background-color: #fff;
  padding: 20rpx 0;
  border-radius: 8px;
  margin-bottom: 16px;
}

.tab {
  position: relative;
  font-size: 14px;
  color: #666;
  padding-bottom: 10rpx;
  text-align: center;
}

.tab.active {
  color: #007aff;
  font-weight: bold;
}

.underline {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 100%;
  /* 根据文字长度自动调整 */
  height: 2px;
  background-color: #007aff;
  border-radius: 1px;
}
</style>
