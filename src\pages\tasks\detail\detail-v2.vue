<template>
  <div v-bind="$attrs">
    <!-- 用户信息 -->
    <userInfo/>

    <!-- 主体区域 -->
    <view class="container" v-if="taskInfo">
      <!-- 任务类型 -->
      <view class="tasks-type">{{ TaskType.getDescription(taskInfo.taskType) }}</view>
      <!-- 任务信息 -->
      <view class="tasks-info">
        <view class="tasks-info-top">
          <view class="task-title">
            <span>整改任务</span>
            <text class="task-date">{{ formatTimeAgo(taskInfo.createdAt) }}</text>
          </view>
          <view class="tasks-no">编号：{{ taskInfo.executePlanId }}-{{ taskInfo.id }}</view>
        </view>
        <view class="task-desc">{{ taskInfo.taskTitle }}</view>
      </view>

      <!-- 巡查地点列表 -->
      <view class="location-list">
        <location-list :locations="taskInfo.location" title="选择当前到达地点（可左右滑动），拍照反馈"
                       :currentIndex="locationIndex" @indexChange="locationIndexChange"/>
      </view>

      <!-- 质控点列表 - 优化后的版本 -->
      <view class="points-list-container">
        <view class="points-title">请点击以下要点，逐项检查核对</view>
        <view class="points-list">
          <view
              v-for="(item, index) in pointsList"
              :key="index"
              class="point-item"
              :class="{
              'status-notdone': item.status === 10,
              'status-done': item.status === 20 && item.feedbackType === 10,
              'status-error': item.status === 20 && item.feedbackType !== 10 && item.correctionType !== 30,
              'status-pending': item.status === 20 && item.feedbackType !== 10 && item.correctionType === 30,
            }"
              @click="openPointsForm(index)"
          >
            <view class="point-status-bg"></view>
            <view class="point-index">{{ index + 1 }}</view>
            <view class="point-name">{{ item.pointsInfo.title }}</view>
            <view class="point-status">
              <view v-if="item.status === 10" class="status-tag status-notdone">
                {{ disabled ? "未检查" : "待检查" }}
              </view>
              <view v-else-if="item.status === 20" class="status-tag status-done">已完成</view>
              <view v-else-if="item.status === 30" class="status-tag status-error">待整改</view>
            </view>
            <!-- 状态水印 -->
            <view class="status-watermark" v-if="item.status === 20">已完成</view>
            <view class="status-watermark error" v-else-if="item.status === 30">待整改</view>
            <!-- 暂存数据提示 -->
            <view class="temp-data-tag" v-if="hasTempData(item.id)"></view>
          </view>
        </view>
      </view>

      <view class="btn-group">
        <CustomButton class="btn-finish" type="primary" text="该地点已完成巡查，上传巡查信息" @click="doLocationFinish"
                   :disabled="currentLocationStatus === 20"/>
        <CustomButton class="btn-publish" type="warning" text="不是我的巡查点，我要上报问题" @click="publishHandler"/>
      </view>
    </view>

    <!-- 表单抽屉 -->
    <CustomPopup
        :show="showFormPopup"
        @close="closeFormPopup"
        mode="bottom"
        :round="32"
        :z-index="99"
        :closeable="false"
        :close-on-click-overlay="false"
        :scrollable="true"
    >
      <view class="drawer-container">
        <view class="drawer-header">
          <view class="drawer-title">
            <view class="title-content">
              <text class="points-index-tag">{{ pointsIndex + 1 }}</text>
              <text class="title-text">{{ currentPoints?.pointsInfo?.description }}</text>
            </view>
          </view>
        </view>

        <view class="drawer-content">
          <view class="custom-form" ref="formRef">
            <!-- 反馈方式选择 -->
            <view class="form-section">
              <!-- 选择项模式 -->
              <view class="feedback-choices" v-if="currentPoints?.pointsInfo?.feedbackWay === 10">
                <CustomRadio
                  v-model="formData.feedbackType"
                  placement="row"
                  shape="square"
                  :disabled="disabled"
                  :options="FeedbackType.getAll()"
                />
                <!-- 调试信息 -->
<!--                <view style="font-size: 24rpx; color: #999; margin-top: 10rpx;" v-if="false">-->
<!--                  调试: disabled={{ disabled }}, currentPoints.status={{ currentPoints?.status }}-->
<!--                </view>-->
              </view>

              <!-- 数量输入模式 -->
              <view v-else class="number-inputs">
                <view class="form-item">
                  <text class="form-label">{{ FeedbackType.getDescription(FeedbackType.FULLY_COMPLIANT) }}</text>
                  <CustomNumberBox
                    v-model="formData.completelyConformingCount"
                    :integer="true"
                    inputWidth="120rpx"
                    :min="0"
                    :disabled="disabled"
                  />
                </view>
                <view class="form-item">
                  <text class="form-label">{{ FeedbackType.getDescription(FeedbackType.PARTIALLY_COMPLIANT) }}</text>
                  <CustomNumberBox
                    v-model="formData.partiallyConformingCount"
                    :integer="true"
                    inputWidth="120rpx"
                    :min="0"
                    :disabled="disabled"
                  />
                </view>
                <view class="form-item">
                  <text class="form-label">{{ FeedbackType.getDescription(FeedbackType.NON_COMPLIANT) }}</text>
                  <CustomNumberBox
                    v-model="formData.nonConformingCount"
                    :integer="true"
                    inputWidth="120rpx"
                    :min="0"
                    :disabled="disabled"
                  />
                </view>
              </view>
            </view>
            <!-- 公共部分 -->
            <view  class="form-section" v-if="!(currentPoints.status === 20 && currentPoints.patrolImage.length === 0)">
              <!-- <view class="section-title">请拍摄巡查照片（最多9张）</view> -->
              <view class="problem-desc-wrapper">
                <!-- 巡查图片-->
                <ImageUploader
                  v-model="formData.patrolImage"
                  title="请拍摄巡查照片（最多9张）"
                  :max-count="9"
                  :disabled="disabled"
                  :show-tip="false"
                />
              </view>
            </view>

            <view v-if="formData.feedbackType !== FeedbackType.FULLY_COMPLIANT">
              <!-- 问题描述输入框 -->
              <view class="form-section">
                <!-- <view class="section-title">问题描述</view> -->
                <view class="problem-desc-wrapper">
                  <CustomInput
                    class="problem-desc-input"
                    placeholder="请详细描述发现的问题"
                    v-model="formData.problemDesc"
                    :disabled="disabled"
                  />
                </view>
              </view>

              <!-- 整改选择 -->
              <view class="form-section">
                <!-- <view class="section-title">整改方式</view> -->
                <view class="section-desc">该地点具有不符合项，请选择是否需要整改</view>
                <view class="radio-wrapper">
                  <CustomRadio
                    v-model="formData.correctionType"
                    placement="row"
                    shape="square"
                    :disabled="disabled"
                    :options="CorrectionType.getAll()"
                  />
                </view>
              </view>

              <!-- 整改后图片 -->
              <view class="form-section" v-if="formData.correctionType === CorrectionType.IMMEDIATE_CORRECTION">
                <ImageUploader
                  v-model="formData.finishImage"
                  title="请拍摄或上传整改后的照片"
                  :max-count="9"
                  :disabled="disabled"
                  :required="true"
                />
              </view>

              <!-- 任务执行人 -->
              <view class="form-section"
                    v-if="formData.correctionType === CorrectionType.FUTURE_CORRECTION
                    && userDetail.authority.canAssign && currentPoints.status === 10">
                <view class="section-title">任务执行人</view>
                <view class="executor-picker">
                  <picker mode="selector" :range="executors" rangeKey="nickName"
                          :disabled="disabled" @change="onExecutorChange">
                    <view class="picker-display">
                      <text class="picker-text">{{ formData.executorName || '请选择任务执行人' }}</text>
                      <text class="picker-arrow">▼</text>
                    </view>
                  </picker>
                </view>
              </view>
            </view>
          </view>
        </view>

        <view class="drawer-footer">
          <CustomButton class="footer-btn close-btn" @click="closeFormPopupWithAutoSave">关闭</CustomButton>
          <CustomButton
              class="footer-btn confirm-btn"
              type="primary"
              @click="onsubmitHandler"
              :disabled="currentPoints?.status === 20 || currentLocationStatus === 20">
            {{ (currentPoints?.status === 20 || currentLocationStatus === 20) ? '已保存' : '确认' }}
          </CustomButton>
        </view>
      </view>
    </CustomPopup>

    <!-- 巡查点验证弹窗 -->
    <view v-if="showVerifyPopup" class="verify-modal-overlay" @click="handleVerifyCancel">
      <view class="verify-modal-container" @click.stop>
        <!-- 标题 -->
        <view class="verify-modal-header">
          <text class="verify-modal-title">{{ verifyTitle }}</text>
        </view>

        <!-- 内容 -->
        <view class="verify-modal-content">
          <view class="slot-content" style="margin-top: 30rpx;">
            <view class="drawer-content">
              <!-- 地点验证图片 -->
              <ImageUploader
                v-model="verifyFormData.locationImage"
                :max-count="1"
                :disabled="disabled"
                :show-tip="false"
                :required="true"
                @change="onLocationImageChange"
              />
            </view>
          </view>
        </view>

        <!-- 按钮组 -->
        <view class="verify-modal-footer">
          <button class="verify-modal-btn cancel-btn" @click="handleVerifyCancel">
            取消
          </button>
          <button
            class="verify-modal-btn confirm-btn"
            :class="{ disabled: isConfirmDisabled }"
            :disabled="isConfirmDisabled"
            @click="submitLocationVerify"
          >
            提交验证
          </button>
        </view>
      </view>
    </view>
  </div>
</template>

<script setup>
import {ref, watch, computed, shallowRef} from 'vue'
import {onLoad} from "@dcloudio/uni-app"
import * as TasksApi from '@/api/tasks'
import LocationList from '@/components/locationList/LocationList.vue'
import ImageUploader from '@/components/ImageUploader/ImageUploader.vue'

import CustomPopup from '@/components/CustomPopup/CustomPopup.vue'
import CustomButton from '@/components/CustomButton/CustomButton.vue'
import CustomInput from '@/components/CustomInput/CustomInput.vue'
import CustomRadio from '@/components/CustomRadio/CustomRadio.vue'
import CustomNumberBox from '@/components/CustomNumberBox/CustomNumberBox.vue'
import {TaskType} from "@/common/enum/tasksType"
import {FeedbackType} from "@/common/enum/feedbackType"
import {CorrectionType} from "@/common/enum/correctionType"
import {formatTimeAgo} from "@/utils/date"
import * as UserApi from "@/api/user";
import {useUserStore} from '@/stores/user'

const store = useUserStore()
const userDetail = shallowRef({})
// 默认获取用户信息（优先缓存）
store.fetchUserInfo().then(user => {
  userDetail.value = user
})

// 任务详情
const taskInfo = ref(null)
// 巡查点索引
const locationIndex = ref(0)
// 巡查点列表
const locationList = ref([])
// 质控点索引
const pointsIndex = ref(0)
// 质控点列表
const pointsList = ref([])
// 选中的之间点
const currentPoints = ref(null)
// 表单弹窗显示状态
const showFormPopup = ref(false)
// 已经巡查过的人数
const completePeopleCount = ref(0)

// 执行人列表
const executors = ref([])

// 是否可选择
const disabled = computed(() => {
  // 只有当前质控点已提交时才禁用，不考虑巡查点状态
  const isDisabled = currentPoints.value?.status === 20
  console.log('disabled 计算:', {
    currentPointsStatus: currentPoints.value?.status,
    locationStatus: locationList.value[locationIndex.value]?.status,
    isDisabled
  })
  return isDisabled
})

// 当前巡查点状态
const currentLocationStatus = computed(() => {
  return locationList.value[locationIndex.value]?.status
})

// 保存巡查地点索引到本地存储
const saveLocationIndex = (tasksId, index) => {
  const key = `task_${tasksId}_location_index`
  try {
    uni.setStorageSync(key, index)
    console.log('保存巡查地点索引:', index)
  } catch (e) {
    console.error('保存巡查地点索引失败:', e)
  }
}

// 获取保存的巡查地点索引
const getSavedLocationIndex = (tasksId) => {
  const key = `task_${tasksId}_location_index`
  try {
    const value = uni.getStorageSync(key)
    console.log('获取保存的巡查地点索引:', value)
    return value !== '' ? Number(value) : null
  } catch (e) {
    console.error('获取保存的巡查地点索引失败:', e)
    return null
  }
}

onLoad((options) => {
  console.log("onLoad 请求参数:", options)
  const tasksId = options.tasksId
  // 获取任务详情
  TasksApi.detail(tasksId).then((res) => {
    taskInfo.value = res.data.tasksInfo
    locationList.value = res.data.tasksInfo.location

    // 尝试从本地存储获取上次选择的巡查地点索引
    const savedIndex = getSavedLocationIndex(tasksId)

    // 如果有保存的索引并且索引有效，则使用该索引
    if (savedIndex !== null && savedIndex >= 0 && savedIndex < res.data.tasksInfo.location.length) {
      locationIndex.value = savedIndex
      // 获取对应巡查点的质控点列表
      let id = locationList.value[savedIndex].id
      TasksApi.getPointsListByLocationID(id).then((pointsRes) => {
        pointsIndex.value = 0
        pointsList.value = pointsRes.data.pointsList
        currentPoints.value = pointsRes.data.currentPoints
        completePeopleCount.value = pointsRes.data.completePeopleCount
        resetFormData()
      })
    } else {
      // 使用后端返回的默认索引
      locationIndex.value = res.data.locationIndex
      pointsIndex.value = res.data.pointsIndex
      pointsList.value = res.data.pointsList
      currentPoints.value = res.data.currentPoints
      completePeopleCount.value = res.data.completePeopleCount
      resetFormData()
    }
    // 检查当前地点是否已验证
    const currentLocation = locationList.value[locationIndex.value]
    if (currentLocation.verifyType === 20 && currentLocation.verifyStatus === 10) {
      showLocationVerifyPopup()
    }
  })
})

// 表单数据
const formData = ref({
  feedbackType: FeedbackType.FULLY_COMPLIANT,    // 符合类型 - 设置默认值
  completelyConformingCount: 0, // 完全符合数量
  partiallyConformingCount: 0, //  部分符合数量
  nonConformingCount: 0, // 不符合数量
  problemDesc: "",  // 问题描述
  correctionType: CorrectionType.IMMEDIATE_CORRECTION, // 整改类型
  patrolImage: [],  //巡查图片
  finishImage: [], // 整改后照片
  executorId: 0 // 后期整改执行人
})

// 保存初始表单状态，用于检测是否有修改
const initialFormState = ref('')

// 检查表单是否有修改
const hasFormChanged = () => {
  const currentState = JSON.stringify({
    feedbackType: formData.value.feedbackType,
    completelyConformingCount: formData.value.completelyConformingCount,
    partiallyConformingCount: formData.value.partiallyConformingCount,
    nonConformingCount: formData.value.nonConformingCount,
    problemDesc: formData.value.problemDesc,
    correctionType: formData.value.correctionType,
    patrolImage: formData.value.patrolImage,
    finishImage: formData.value.finishImage,
    executorId: formData.value.executorId,
    executorName: formData.value.executorName
  })

  return currentState !== initialFormState.value
}

// 初始化数据
const resetFormData = () => {
  if (!currentPoints.value) return

  // 添加调试信息
  console.log('resetFormData - currentPoints.value:', currentPoints.value)
  console.log('resetFormData - status:', currentPoints.value.status)
  console.log('resetFormData - feedbackType:', currentPoints.value.feedbackType)
  console.log('resetFormData - correctionType:', currentPoints.value.correctionType)

  if (currentPoints.value.status !== 10) {
    // 已提交的数据，从后端获取
    formData.value = {
      feedbackType: currentPoints.value.feedbackType || FeedbackType.FULLY_COMPLIANT,    // 符合类型，提供默认值
      completelyConformingCount: currentPoints.value.completelyConformingCount || 0, // 完全符合数量
      partiallyConformingCount: currentPoints.value.partiallyConformingCount || 0, // 部分符合数量
      nonConformingCount: currentPoints.value.nonConformingCount || 0, // 不符合数量
      problemDesc: currentPoints.value.problemDesc || "",  // 问题描述，提供默认值
      correctionType: currentPoints.value.correctionType || CorrectionType.IMMEDIATE_CORRECTION, // 整改类型，提供默认值
      patrolImage: currentPoints.value.patrolImage?.map(item => ({url: item.imageUrl})) || [],  // 巡查图片
      finishImage: currentPoints.value.finishImage?.map(item => ({url: item.imageUrl})) || [], // 整改后照片
      executorId: currentPoints.value.executorId || 0,
      executorName: currentPoints.value.executorName || ''
    }
  } else {
    // 新数据，使用默认值
    formData.value = {
      feedbackType: FeedbackType.FULLY_COMPLIANT,    // 符合类型
      completelyConformingCount: 0, // 完全符合数量
      partiallyConformingCount: 0, // 部分符合数量
      nonConformingCount: 0, // 不符合数量
      problemDesc: "",  // 问题描述
      correctionType: CorrectionType.IMMEDIATE_CORRECTION, // 整改类型
      patrolImage: [],  // 巡查图片
      finishImage: [], // 整改后照片
      executorId: 0,
      executorName: ''
    }
  }

  // 添加调试信息
  console.log('resetFormData - 设置后的 formData:', formData.value)

  // 设置表单初始状态
  setTimeout(() => {
    initialFormState.value = JSON.stringify({
      feedbackType: formData.value.feedbackType,
      completelyConformingCount: formData.value.completelyConformingCount,
      partiallyConformingCount: formData.value.partiallyConformingCount,
      nonConformingCount: formData.value.nonConformingCount,
      problemDesc: formData.value.problemDesc,
      correctionType: formData.value.correctionType,
      patrolImage: formData.value.patrolImage,
      finishImage: formData.value.finishImage,
      executorId: formData.value.executorId,
      executorName: formData.value.executorName
    })
  }, 100)
}

// 监听 - 修复递归更新问题
watch(
    () => [formData.value.completelyConformingCount, formData.value.partiallyConformingCount, formData.value.nonConformingCount],
    (newValues, oldValues) => {
      if (!currentPoints.value || currentPoints.value.pointsInfo.feedbackWay === 10) return

      // 赋值 解决null不能比较的问题
      let completelyConformingCount = newValues[0] ?? 0
      let partiallyConformingCount = newValues[1] ?? 0
      let nonConformingCount = newValues[2] ?? 0

      // 计算新的 feedbackType
      let newFeedbackType

      // 完全符合
      if (partiallyConformingCount === 0 && nonConformingCount === 0) {
        newFeedbackType = FeedbackType.FULLY_COMPLIANT
      }
      // 部分符合
      else if (partiallyConformingCount !== 0 || nonConformingCount !== 0) {
        newFeedbackType = FeedbackType.PARTIALLY_COMPLIANT
      }
      // 完全不符合
      else if (completelyConformingCount === 0 && partiallyConformingCount === 0) {
        newFeedbackType = FeedbackType.NON_COMPLIANT
      }

      // 只有当值真正改变时才更新，避免递归
      if (newFeedbackType !== undefined && formData.value.feedbackType !== newFeedbackType) {
        formData.value.feedbackType = newFeedbackType
      }
    }
)

// 切换巡查点
const locationIndexChange = (index) => {
  console.log("巡查点切换索引：：", index)
  console.log("巡查点名称：", taskInfo.value.location[index].locationName)
  // 获取列表
  let id = locationList.value[index].id
  TasksApi.getPointsListByLocationID(id).then((res) => {
    locationIndex.value = index // 修改索引
    pointsIndex.value = 0     // 重置索引
    pointsList.value = res.data.pointsList// 质控点列表
    currentPoints.value = res.data.currentPoints  // 首个质控点详情

    // 重置formData
    resetFormData()
    // 关闭弹窗
    showFormPopup.value = false

    // 保存当前选择的巡查地点索引
    if (taskInfo.value && taskInfo.value.id) {
      saveLocationIndex(taskInfo.value.id, index)
    }

    // 验证当前巡查点是否需要验证
    const currentLocation = locationList.value[locationIndex.value]
    if (currentLocation.verifyType === 20 && currentLocation.verifyStatus === 10) {
      showLocationVerifyPopup()
    }

  })
}

// 打开质控点表单弹窗
const openPointsForm = (index) => {
  // 保存质控点索引，用于验证后继续打开
  pointsIndex.value = index

  // 检查当前地点是否已验证
  const currentLocation = locationList.value[locationIndex.value]
  if (currentLocation.verifyType === 20 && currentLocation.verifyStatus === 10) {
    showLocationVerifyPopup()
    return
  }

  // 获取【后期整改】可执行人
  getExecutors(userDetail.value.hospitalId)
  // 获取质控点信息
  let id = pointsList.value[index].id
  TasksApi.getPointsDetail(id).then((res) => {
    currentPoints.value = res.data.currentPoints
    completePeopleCount.value = res.data.completePeopleCount

    // 检查是否有暂存数据
    checkTempData(id)

    // 显示弹窗
    showFormPopup.value = true
  })
}

// 检查是否有暂存数据
const checkTempData = (pointsId) => {
  try {
    const storageKey = `points_temp_data_${pointsId}`
    const tempDataStr = uni.getStorageSync(storageKey)

    if (tempDataStr) {
      const tempData = JSON.parse(tempDataStr)

      console.log('checkTempData - 暂存数据:', tempData)

      // 直接加载暂存数据，不需要用户确认
      formData.value = {
        feedbackType: tempData.feedbackType || FeedbackType.FULLY_COMPLIANT,
        completelyConformingCount: tempData.completelyConformingCount || 0,
        partiallyConformingCount: tempData.partiallyConformingCount || 0,
        nonConformingCount: tempData.nonConformingCount || 0,
        problemDesc: tempData.problemDesc || '',
        correctionType: tempData.correctionType || CorrectionType.IMMEDIATE_CORRECTION,
        patrolImage: tempData.patrolImage || [],
        finishImage: tempData.finishImage || [],
        executorId: tempData.executorId || 0,
        executorName: tempData.executorName || ''
      }

      console.log('checkTempData - 加载后的 formData:', formData.value)

    } else {
      // 没有暂存数据，使用初始数据
      resetFormData()
    }
  } catch (e) {
    console.error('读取暂存数据失败:', e)
    // 发生错误时使用初始数据
    resetFormData()
  }
}

// 地点验证弹窗状态
const showVerifyPopup = ref(false)
// 地点验证表单数据
const verifyFormData = ref({
  locationImage: []
})

// 监控弹窗状态变化
watch(showVerifyPopup, (newVal, oldVal) => {
  console.log('showVerifyPopup 状态变化:', oldVal, '->', newVal)
}, { immediate: true })

// 监控 disabled 状态变化
watch(disabled, (newVal, oldVal) => {
  console.log('disabled 状态变化:', oldVal, '->', newVal)
  console.log('当前 currentPoints.status:', currentPoints.value?.status)
  console.log('当前 location.status:', locationList.value[locationIndex.value]?.status)
}, { immediate: true })

const verifyTitle = computed(() => {
  if (locationList.value.length === 0) return
  return '【' + locationList.value[locationIndex.value].locationName + '】必须上传照片'
})

// 添加调试用的计算属性
const isConfirmDisabled = computed(() => {
  const disabled = verifyFormData.value.locationImage.length === 0
  console.log('isConfirmDisabled:', disabled, 'imageLength:', verifyFormData.value.locationImage.length)
  return disabled
})

// 显示地点验证弹窗
const showLocationVerifyPopup = () => {
  console.log('showLocationVerifyPopup 被调用')
  console.log('当前 showVerifyPopup.value:', showVerifyPopup.value)

  // 如果弹窗已经显示，不重复显示
  if (showVerifyPopup.value) {
    console.log('弹窗已经显示，跳过')
    return
  }

  showVerifyPopup.value = true
  console.log('设置后 showVerifyPopup.value:', showVerifyPopup.value)
}

// 处理验证弹窗取消事件
const handleVerifyCancel = () => {
  console.log('handleVerifyCancel 被调用')
  closeVerifyPopup()
}

// 关闭地点验证弹窗
const closeVerifyPopup = () => {
  console.log('closeVerifyPopup 被调用')
  console.log('关闭前 showVerifyPopup.value:', showVerifyPopup.value)

  // 强制设置为 false
  showVerifyPopup.value = false

  // 清空验证表单数据
  verifyFormData.value.locationImage = []
  console.log('验证表单数据已清空')
  console.log('关闭后 showVerifyPopup.value:', showVerifyPopup.value)
}

// 提交地点验证
const submitLocationVerify = () => {
  console.log('submitLocationVerify 被调用')
  console.log('verifyFormData.value.locationImage:', verifyFormData.value.locationImage)

  if (verifyFormData.value.locationImage.length === 0) {
    uni.showToast({
      title: '请至少上传一张验证照片',
      icon: 'none'
    })
    return
  }

  // 检查地点验证图片上传状态
  const uploadingLocationImages = verifyFormData.value.locationImage.filter(img => img.status === 'uploading')
  if (uploadingLocationImages.length > 0) {
    uni.showToast({
      title: '请等待验证图片上传完成',
      icon: 'none'
    })
    return
  }

  const errorLocationImages = verifyFormData.value.locationImage.filter(img => img.status === 'error')
  if (errorLocationImages.length > 0) {
    uni.showToast({
      title: '存在上传失败的验证图片，请重新上传',
      icon: 'none'
    })
    return
  }

  // 获取当前巡查点
  const currentLocation = locationList.value[locationIndex.value]

  // 保存当前选中的质控点索引，用于验证完成后继续打开
  const currentPointIndex = pointsIndex.value

  // 这里可以添加验证API调用
  // 模拟API调用 - 实际使用时替换为API调用
  uni.showLoading({
    title: '正在验证...'
  })

  // 获取成功上传的图片URL
  const successImages = verifyFormData.value.locationImage.filter(item => item.status === 'success' && item.url)
  if (successImages.length === 0) {
    uni.showToast({
      title: '没有可用的验证图片',
      icon: 'none'
    })
    return
  }

  // 构建请求参数
  const params = {
    locationId: currentLocation.id,
    content: successImages[0].url
  }

  // 请求
  TasksApi.locationVerify(params).then(() => {
    // 隐藏loading
    uni.hideLoading()

    uni.showToast({
      title: '地点验证成功',
      icon: 'success'
    })

    // 清空验证表单数据
    verifyFormData.value.locationImage = []

    closeVerifyPopup()

    // 验证成功后，继续打开之前点击的质控点表单
    if (currentPointIndex >= 0 && currentPointIndex < pointsList.value.length) {
      // 状态设置为已验证
      locationList.value[locationIndex.value].verifyStatus = 20
      // 获取【后期整改】可执行人
      getExecutors(userDetail.value.hospitalId)
      // 获取质控点信息
      let id = pointsList.value[currentPointIndex].id
      TasksApi.getPointsDetail(id).then((res) => {
        pointsIndex.value = currentPointIndex
        currentPoints.value = res.data.currentPoints
        completePeopleCount.value = res.data.completePeopleCount
        // 重置formData
        resetFormData()
        // 显示弹窗
        showFormPopup.value = true
      })
    }
  }).catch((error) => {
    // 隐藏loading
    uni.hideLoading()

    console.error('地点验证失败:', error)
    uni.showToast({
      title: '验证失败，请重试',
      icon: 'none'
    })
  })
}

// 关闭表单弹窗（自动暂存数据）
const closeFormPopupWithAutoSave = () => {
  // 检查用户是否修改了内容
  if (hasFormChanged()) {
    // 自动暂存数据
    saveFormData()
  } else {
    // 未修改直接关闭
    showFormPopup.value = false
  }
}

// 暂存表单数据
const saveFormData = () => {
  try {
    // 输出当前质控点ID，用于调试
    console.log('保存暂存数据 - 当前质控点ID:', currentPoints.value.id)

    // 构建暂存数据
    const tempData = {
      pointsId: currentPoints.value.id,
      feedbackType: formData.value.feedbackType,
      completelyConformingCount: formData.value.completelyConformingCount || 0,
      partiallyConformingCount: formData.value.partiallyConformingCount || 0,
      nonConformingCount: formData.value.nonConformingCount || 0,
      problemDesc: formData.value.problemDesc,
      correctionType: formData.value.correctionType,
      patrolImage: formData.value.patrolImage,
      finishImage: formData.value.finishImage,
      executorId: formData.value.executorId,
      executorName: formData.value.executorName
    }

    // 存储到本地
    const storageKey = `points_temp_data_${currentPoints.value.id}`
    uni.setStorageSync(storageKey, JSON.stringify(tempData))
    console.log('暂存数据已保存:', storageKey)

    // 通过创建新数组触发Vue的响应式更新，确保暂存标签显示
    pointsList.value = [...pointsList.value]

    // 关闭弹窗
    showFormPopup.value = false
  } catch (e) {
    console.error('暂存数据失败:', e)
    uni.showToast({
      title: '暂存失败',
      icon: 'none'
    })
  }
}

// 关闭表单弹窗（直接关闭，不做任何处理）
const closeFormPopup = () => {
  showFormPopup.value = false
}

// 保存
const onsubmitHandler = () => {
  console.log("保存表单")
  console.log("整改前照片：", formData.value.patrolImage)
  console.log("整改后照片：", formData.value.finishImage)
  console.log("表单数据", formData.value)
  // 数据校验
  if (!checkoutCommit()) return
  // 请求参数
  let params = {
    id: currentPoints.value.id,
    feedbackType: formData.value.feedbackType,
    completelyConformingCount: formData.value.completelyConformingCount || 0,
    partiallyConformingCount: formData.value.partiallyConformingCount || 0,
    nonConformingCount: formData.value.nonConformingCount || 0,
    problemDesc: formData.value.problemDesc,
    correctionType: formData.value.correctionType,
    patrolImage: formData.value.patrolImage
      .filter(item => item.status === 'success' && item.url)
      .map(item => item.url),
    finishImage: formData.value.finishImage
      .filter(item => item.status === 'success' && item.url)
      .map(item => item.url),
    executorId: formData.value.executorId,
  }
  console.log("请求参数>>", params)

  uni.showModal({
    title: '确认完成',
    content: '确认完成该质控点吗？',
    confirmText: '确定',
    cancelText: '取消',
    success: async (res) => {
      if (res.confirm) {
        // 请求
        TasksApi.submit(params).then(res => {
          // 提交成功
          currentPoints.value.status = 20
          pointsList.value[pointsIndex.value].status = 20
          pointsList.value[pointsIndex.value].feedbackType = formData.value.feedbackType
          pointsList.value[pointsIndex.value].correctionType = formData.value.correctionType

          // 清除暂存数据
          clearTempData(currentPoints.value.id)

          // 提示
          uni.showToast({
            icon: "none",
            mask: true,
            title: res.message,
          })
          // 关闭弹窗
          closeFormPopup()
        })
      }
    }
  })
}

// 清除暂存数据
const clearTempData = (pointsId) => {
  try {
    const storageKey = `points_temp_data_${pointsId}`
    uni.removeStorageSync(storageKey)
    console.log('已清除暂存数据:', pointsId)

    // 通过创建新数组触发Vue的响应式更新，确保暂存标签被移除
    pointsList.value = [...pointsList.value]
  } catch (e) {
    console.error('清除暂存数据失败:', e)
  }
}

// 修改 onExecutorChange 函数
const onExecutorChange = (e) => {
  let index = e.detail.value
  if (index >= 0 && index < executors.value.length) {
    const selectedExecutor = executors.value[index]
    formData.value.executorName = selectedExecutor.nickName
    formData.value.executorId = selectedExecutor.id
  }
}

// 图片变化处理函数
// const onPatrolImageChange = (imageList) => {
//   console.log('巡查图片变化:', imageList)
//   formData.value.patrolImage = imageList
// }

const onFinishImageChange = (imageList) => {
  console.log('整改后图片变化:', imageList)
  formData.value.finishImage = imageList
}

const onLocationImageChange = (imageList) => {
  console.log('地点验证图片变化:', imageList)
  verifyFormData.value.locationImage = imageList
}

// 验证参数
const checkoutCommit = () => {
  // 巡查图片
  if (formData.value.feedbackType !== FeedbackType.FULLY_COMPLIANT && formData.value.patrolImage.length === 0) {
    uni.showToast({
      title: '至少上传一张巡查照片',
      icon: 'none'
    })
    return false
  }

  // 检查巡查图片上传状态
  if (formData.value.feedbackType !== FeedbackType.FULLY_COMPLIANT) {
    const uploadingPatrolImages = formData.value.patrolImage.filter(img => img.status === 'uploading')
    if (uploadingPatrolImages.length > 0) {
      uni.showToast({
        title: '请等待巡查图片上传完成',
        icon: 'none'
      })
      return false
    }

    const errorPatrolImages = formData.value.patrolImage.filter(img => img.status === 'error')
    if (errorPatrolImages.length > 0) {
      uni.showToast({
        title: '存在上传失败的巡查图片，请重新上传',
        icon: 'none'
      })
      return false
    }
  }

  // 立即整改
  if (formData.value.feedbackType !== FeedbackType.FULLY_COMPLIANT &&
      formData.value.correctionType === CorrectionType.IMMEDIATE_CORRECTION &&
      formData.value.finishImage.length === 0) {

    uni.showToast({
      title: '至少上传一张整改后照片',
      icon: 'none'
    })
    return false
  }

  // 检查整改后图片上传状态
  if (formData.value.feedbackType !== FeedbackType.FULLY_COMPLIANT &&
      formData.value.correctionType === CorrectionType.IMMEDIATE_CORRECTION) {
    const uploadingFinishImages = formData.value.finishImage.filter(img => img.status === 'uploading')
    if (uploadingFinishImages.length > 0) {
      uni.showToast({
        title: '请等待整改后图片上传完成',
        icon: 'none'
      })
      return false
    }

    const errorFinishImages = formData.value.finishImage.filter(img => img.status === 'error')
    if (errorFinishImages.length > 0) {
      uni.showToast({
        title: '存在上传失败的整改后图片，请重新上传',
        icon: 'none'
      })
      return false
    }
  }

  return true
}

// 完成巡查点
const doLocationFinish = async () => {
  // 获取当前巡查点的 id
  const currentLocation = locationList.value[locationIndex.value]

  // 添加确认弹窗
  uni.showModal({
    title: '确认完成',
    content: '确定已完成该巡查点的所有检查项目吗？',
    confirmText: '确定',
    cancelText: '取消',
    success: async (res) => {
      if (res.confirm) {
        try {
          const result = await TasksApi.locationFinish(currentLocation.id)
          if (result.code === 200) {
            uni.showToast({
              title: '巡查点已完成',
              icon: 'success'
            })
            // 更新当前巡查点状态
            currentLocation.status = 20
          } else {
            uni.showToast({
              title: result.message || '操作失败',
              icon: 'none'
            })
          }
        } catch (error) {
          console.error('完成巡查点失败:', error)
          uni.showToast({
            title: '操作失败',
            icon: 'none'
          })
        }
      }
    }
  })
}

// 跳转到上报页面
const publishHandler = () => {
  uni.showModal({
    title: '确认跳转',
    content: '确定跳转到上报页面吗？',
    confirmText: '确定',
    cancelText: '取消',
    success: async (res) => {
      if (res.confirm) {
        uni.redirectTo({
          url: '/pages/tasks/publish/index'
        })
      }
    }
  })
}

// 在获取执行人列表后初始化过滤后的列表
const getExecutors = (hospitalId) => {
  if (executors.value.length > 0) return
  UserApi.getUserListByHospitalId(hospitalId).then((res) => {
    executors.value = res.data
  })
}

// 检查是否有暂存数据
const hasTempData = (pointsId) => {
  try {
    // 添加日志，检查传入的ID
    console.log('检查暂存数据ID:', pointsId)

    const storageKey = `points_temp_data_${pointsId}`
    const tempDataStr = uni.getStorageSync(storageKey)

    // 添加日志，检查读取结果
    console.log('暂存数据读取结果:', !!tempDataStr, storageKey)

    return !!tempDataStr
  } catch (e) {
    console.error('检查暂存数据失败:', e)
    return false
  }
}

</script>

<style scoped>
@import './static/detail2.css';

/* 抽屉容器样式 */
.drawer-container {
  background: #f5f5f5;
  border-radius: 32rpx 32rpx 0 0;
  overflow: hidden;
}

.drawer-header {
  background: #fff;
  padding: 24rpx;
  border-bottom: 1rpx solid #e5e5e5;
}

.drawer-title {
  display: flex;
  align-items: center;
}

.title-content {
  display: flex;
  align-items: center;
  width: 100%;
}

.title-text {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-left: 12rpx;
  flex: 1;
  line-height: 1.4;
}

.drawer-content {
  padding: 20rpx;
  max-height: 80vh;
  overflow-y: auto;
  background: #f5f5f5;
}

/* 自定义表单样式 */
.custom-form {
  background: transparent;
}

/* 表单项通用样式 */
.form-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
  padding: 20rpx;
  background: #fff;
  border-radius: 8rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 400;
  flex-shrink: 0;
  min-width: 200rpx;
}

/* 反馈选择样式 */
.feedback-choices {
  margin-top: 16rpx;
}

/* 数字输入区域 */
.number-inputs {
  margin-top: 16rpx;
}

.number-inputs .form-item {
  background: #fff;
  margin-bottom: 12rpx;
  padding: 20rpx;
  border-radius: 8rpx;
}

.number-inputs .form-item:last-child {
  margin-bottom: 0;
}

.number-inputs .form-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 400;
  min-width: 240rpx;
}

/* 表单分组样式 */
.form-section {
  margin-bottom: 20rpx;
  padding: 20rpx;
  background: #fff;
  border-radius: 8rpx;
}

.section-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 16rpx;
}

.section-desc {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 16rpx;
  line-height: 1.4;
}

/* 问题描述输入框样式 */
.problem-desc-wrapper {
  margin-top: 16rpx;
}

.problem-desc-input {
  width: 100%;
  min-height: 120rpx;
}

/* 自定义输入框样式 - 与 publish/index.vue 保持一致 */
:deep(.custom-input-wrapper) {
  width: 100%;
  border-radius: 8rpx;
  overflow: hidden;
  box-sizing: border-box;
  max-width: 100%;
}

:deep(.custom-input) {
  width: 100% !important;
  max-width: 100% !important;
  height: 88rpx !important;
  font-size: 30rpx !important;
  line-height: 1.4 !important;
  color: #000 !important;
  background: white !important;
  box-sizing: border-box !important;
  padding: 0 20rpx !important;
  border: none !important;
  outline: none !important;
  pointer-events: auto !important;
}

:deep(.input-surround) {
  border: 2rpx solid #d9d9d9 !important;
  border-radius: 8rpx !important;
}

:deep(.input-focus.input-surround) {
  border-color: #1890ff !important;
  box-shadow: 0 0 0 4rpx rgba(24, 144, 255, 0.1) !important;
}

/* 单选框包装器 */
.radio-wrapper {
  margin-top: 16rpx;
}

/* 执行人选择器样式 */
.executor-picker {
  margin-top: 16rpx;
}

.picker-display {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  background: #f5f5f5;
  border-radius: 8rpx;
  min-height: 80rpx;
  box-sizing: border-box;
}

.picker-text {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

.picker-arrow {
  font-size: 22rpx;
  color: #999;
  margin-left: 12rpx;
}

/* 底部按钮样式 */
.drawer-footer {
  display: flex;
  justify-content: space-between;
  padding: 20rpx;
  background: #fff;
  border-top: 1rpx solid #e5e5e5;
}

.footer-btn {
  flex: 1;
  margin: 0 6rpx;
  height: 80rpx;
  border-radius: 8rpx;
  font-size: 30rpx;
}

.close-btn {
  background-color: #f5f5f5;
  color: #666;
  border: none;
}

.save-btn {
  background-color: #ffe1b3;
  color: #c28039;
  border: none;
}

.confirm-btn {
  flex: 2;
  margin-left: 12rpx;
}

/* 确保质控点项有相对定位 */
.point-item {
  position: relative;
}

/* 暂存数据提示样式 */
.temp-data-tag {
  width: 10rpx;
  height: 10rpx;
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  background-color: #ff9800;
  color: #fff;
  border-radius: 100%;
  z-index: 10;
  font-weight: bold;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* 验证弹窗样式 */
.verify-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.verify-modal-container {
  background: white;
  border-radius: 16rpx;
  width: 90%;
  max-width: 600rpx;
  max-height: 80vh;
  overflow: hidden;
}

.verify-modal-header {
  padding: 32rpx 24rpx 16rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.verify-modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  text-align: center;
}

.verify-modal-content {
  padding: 24rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.verify-modal-footer {
  display: flex;
  border-top: 1rpx solid #f0f0f0;
}

.verify-modal-btn {
  flex: 1;
  height: 88rpx;
  border: none;
  background: none;
  font-size: 30rpx;
  cursor: pointer;
  transition: all 0.3s ease;
}

.verify-modal-btn.cancel-btn {
  color: #666;
  border-right: 1rpx solid #f0f0f0;
}

.verify-modal-btn.cancel-btn:active {
  background: #f5f5f5;
}

.verify-modal-btn.confirm-btn {
  color: #1890ff;
}

.verify-modal-btn.confirm-btn:active {
  background: #f0f8ff;
}

.verify-modal-btn.confirm-btn.disabled {
  color: #d9d9d9;
  cursor: not-allowed;
}

.verify-modal-btn.confirm-btn.disabled:active {
  background: none;
}
</style>
