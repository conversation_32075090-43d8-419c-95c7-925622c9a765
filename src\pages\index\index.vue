<template>
	<view></view>
</template>

<script setup>
import { onLoad } from '@dcloudio/uni-app'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()

onLoad(() => {
	// 检查登录状态
	if (userStore.isLogin) {
		// 已登录，直接跳转到任务列表页
		uni.redirectTo({
			url: '/pages/tasks/list/list'
		})
	} else {
		// 未登录，跳转到登录页
		uni.redirectTo({
			url: '/pages/login/index'
		})
	}
})
</script>

<style>
</style>
