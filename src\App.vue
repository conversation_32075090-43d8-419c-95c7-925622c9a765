<script>

export default {
  async onLaunch() {
    console.log('🚀 App Launch')

    // 初始化推送服务
    // #ifdef APP-PLUS
    try {
      await this.$push.init()
    } catch (error) {
      console.error('推送初始化失败:', error)
    }
    // #endif
  },

  onShow() {
    console.log('👀 App Show')

    // 清除应用角标
    // #ifdef APP-PLUS
    plus.runtime.setBadgeNumber(0)
    // #endif
  },

  onHide() {
    console.log('🙈 App Hide')
  },
}
</script>

<style lang="scss">
/* 注意要写在第一行，同时给style标签加入lang="scss"属性 */
//@import "uview-plus/index.scss";
</style>
